<?php

namespace App\Services\Intl;

use App\Models\OrderModel;
use App\Models\PnrPassengerModel;
use App\Models\TicketBookOrderModel;
use App\Models\TicketRefundOrderModel;

class Refund extends IntlBaseService
{
    protected function getOrderModel()
    {
        return model('TicketRefundOrderModel');
    }

    protected function getOrderType(): string
    {
        return 'refund';
    }

    /**
     * 退票申请
     *
     * @param $params
     *
     * @return array
     * @throws \Exception
     */
    public function apply($params): array
    {
        $orderModel        = model('TicketBookOrderModel');
        $orderSegmentModel = model('TicketBookSegModel');

        // 订单信息
        $orderId = intval($params['order_id']);
        $order   = $orderModel->find($orderId);
        if (empty($order)) {
            error(0, '订单不存在');
        }
        if (!in_array($order['status'], [OrderModel::ORDER_STATUS_TICKETED, OrderModel::ORDER_STATUS_PART_TICKET])) {
            error(0, '只有【已出票】【部分出票】状态才可申请退票');
        }

        // 构建订单基础信息（简化版，用于退款申请）
        $user       = $this->getUser($order['operator_id']);
        $department = $this->getDepartment($user['department_id']);
        $orderData  = [
            'order_no'          => $order['order_no'],
            'pnr'               => $order['pnr'],
            'customer_type'     => OrderModel::CUSTOMER_TYPES[$order['customer_type']],
            'created_at'        => date('Y-m-d H:i:s', $order['created_at']),
            'operator_name'     => $department['department_name'] . '|' . $user['name'],
            'journey_type'      => $order['journey_type'],
            'journey_type_text' => OrderModel::JOURNEY_TYPES[$order['journey_type']],
        ];

        // 获取航班信息
        $orderSegments = $orderSegmentModel->where('order_id', $orderId)->findAll();
        $flightOut     = $this->buildFlightData($orderSegments, $order['journey_type']);

        // 构建乘客信息
        $passengerData = $this->buildPassengerData($orderId, 'apply');

        return [
            'order_data'     => $orderData,
            'flight_data'    => $flightOut,
            'passenger_data' => $passengerData,
        ];
    }

    /**
     * 初始化数据库模型
     *
     * @return array 模型实例数组
     */
    private function _initDbModels(): array
    {
        return [
            'orderPassengerSegmentModel'       => model('TicketBookPaxSegModel'),
            'refundOrderPassengerSegmentModel' => model('TicketRefundPaxSegModel'),
            'orderPassengerModel'              => model('TicketBookPaxModel'),
            'refundOrderPassengerModel'        => model('TicketRefundPaxModel'),
            'orderModel'                       => model('TicketBookOrderModel'),
            'refundOrderModel'                 => model('TicketRefundOrderModel'),
            'orderSegmentModel'                => model('TicketBookSegModel'),
            'refundOrderSegmentModel'          => model('TicketRefundSegModel'),
            'orderDetailModel'                 => model('TicketBookOrderDetailModel'),
            'refundOrderDetailModel'           => model('TicketRefundOrderDetailModel'),
            'pnrModel'                         => model('PnrModel'),
        ];
    }

    /**
     * 确认执行退票操作
     *
     * 执行退票费用校验、退款接口调用及数据库事务处理
     *
     * @param  array  $params  退票请求参数，需包含tickets数组
     *
     * @return array 返回退票操作结果
     * @throws \Exception 当退票校验失败或数据库操作异常时抛出
     */
    public function confirm(array $params): array
    {
        // 获取请求中的票务数据并建立索引
        $postTickets = $params['tickets'];
        $postTickets = array_column($postTickets, null, 'ticket_id');
        $ticketIds   = array_column($postTickets, 'ticket_id');

        // 初始化数据库模型
        $models = $this->_initDbModels();
        extract($models);

        // 加载票务服务
        /** @var \App\Services\Ticket\Ticket $ticketService */
        $ticketService = load_service('Ticket\Ticket');

        //校验票号状态
        $orderPassengers = $orderPassengerModel
            ->whereIn('id', $ticketIds)
            ->where('order_id', $params['order_id'])
            ->where('status', 1)
            ->findAll();
        if (count($orderPassengers) != count($ticketIds)) {
            error(0, '票号状态有变动，请刷新页面后重试-1');
        }

        //校验电子客票航段状态
        $orderPassengerSegments = $orderPassengerSegmentModel->whereIn('passenger_id', $ticketIds)->where('status', 1)->findAll();
        if (empty($orderPassengerSegments)) {
            error(0, '票号状态有变动，票号状态是【可使用(OPEN FOR USE)】才允许退票');
        }
        $allowCount = count(array_unique(array_column($orderPassengerSegments, 'passenger_id')));
        if ($allowCount != count($ticketIds)) {
            error(0, '票号状态有变动，请刷新页面后重试-2');
        }

        // 获取出票订单总预订人数
        $tmpPassenger    = $orderPassengerModel->where('id', $orderPassengerSegments[0]['passenger_id'])->first();
        $order           = $orderModel->where('id', $tmpPassenger['order_id'])->first();
        $passengerNumber = $order['passenger_number'];
        $orderPassenger  = $orderPassengerModel->where('id', $ticketIds)->findAll();

        // 开启数据库事务
        $db = db_connect();
        try {
            // 创建分离PNR用于退票（部分退需要分离PNR，全部退不需要）、
            $isSplitPnr = false;
            if ($passengerNumber > $allowCount) {
                $isSplitPnr     = true;
                $splitPassenger = [];
                foreach ($orderPassenger as $passenger) {
                    $arrPassenger[] = [
                        'passenger_type_code'   => PnrPassengerModel::PASSENGER_TYPE_MAP[$passenger['passenger_type']],
                        'language_type'         => 'ZH',
                        'surname'               => $passenger['person_name'],
                        'doc'                   => [
                            'doc_type' => 'NI',
                            'doc_id'   => $passenger['doc_id'],
                        ],
                        'rph'                   => $passenger['rph'],
                        'accompanied_by_infant' => false,
                    ];
                }
                $splitParams = [
                    'pnr'        => $order['pnr'],
                    'passengers' => $arrPassenger,
                ];
                $booking     = new \App\Libraries\Api\IBE\Booking();
                $pnr         = $booking->split_pnr($splitParams);

                $pnrInfo                     = $pnrModel->where('order_id', $order['id'])->first();
                $pnrInfo['origin_pnr']       = $pnrInfo['pnr'];
                $pnrInfo['pnr']              = $pnr;
                $pnrInfo['passenger_number'] = $allowCount;
                $pnrInfo['created_at']       = time();
                $pnrInfo['updated_at']       = time();
                unset($pnrInfo['id']);
                $pnrId = $pnrModel->insert($pnrInfo);
            }

            // 1. 查询最新退票费用并与提交数据校验
            $queryRes = $ticketService->query_refund_fee($ticketIds, TicketRefundOrderModel::TICKET_TYPE_INTERNATIONAL);
            //提交上来的价格，和接口返回的最新价格进行比对
            foreach ($queryRes as $val) {
                $ticketId = $val['ticket_id'];
                if ($postTickets[$ticketId]['deduction'] != $val['deduction']) {
                    error(0, "退票费不一致,页面提交：{$postTickets[$ticketId]['deduction']},而接口返回的：{$val['deduction']}");
                }
            }

            //2.退票
            $refundRes = $ticketService->refund_ticket($queryRes);
            if (empty($refundRes)) {
                error(0, '退票失败');
            }

            $passengerIds   = array_column($refundRes, 'ticket_id');
            $countPassenger = count($passengerIds);//乘客数量
            $refundRes      = array_column($refundRes, null, 'ticket_id');

            $orderPassengers = $orderPassengerModel->whereIn('id', $passengerIds)->findAll();
            $passengerNames  = implode(',', array_column($orderPassengers, 'person_name'));//乘客姓名，用逗号连接

            $orderPassengers = array_column($orderPassengers, null, 'id');
            $orderDetail     = $orderDetailModel->whereIn('order_passenger_id', $passengerIds)->findAll();
            $oldOrderId      = $orderDetail[0]['order_id'];

            //旧订单信息
            $oldOrder = $orderModel->find($oldOrderId);
            //采购价总额(各类型乘客的价格总额)
            $totalSupplierPrice = 0;
            //总采购金额(采购价总额+税费总额)
            $totalSupplierAmount = 0;
            $totalCustomerAmount = 0;
            //各税费总额
            $totalTax = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];

            // TODO 如果不退一部分，则要分离PNR

            //生成订单号
            $newOrderNo     = $orderModel->generate_order_no('R');
            $addOrderDetail = [];
            foreach ($orderDetail as $val) {
                $totalSupplierPrice = 0;

                $orderPassengerId = $val['order_passenger_id'];
                $interfaceRefund  = $refundRes[$orderPassengerId];//接口返回的
                //采购价 = 原采购价 - 退票费 - 手续费(默认0)
                $supplierPrice = bcsub($val['ticket_supplier_price'], $interfaceRefund['deduction'], 2);
                $supplierPrice = bcsub($supplierPrice, 0, 2);

                $totalSupplierPrice = bcadd($totalSupplierPrice, $supplierPrice, 2);
                //税费
                $taxArr = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];
                foreach ($interfaceRefund['taxes'] as $val1) {
                    $taxArr[$val1['tax_code']]   = $val1['amount'];
                    $totalTax[$val1['tax_code']] = bcadd($totalTax[$val1['tax_code']], $val1['amount'], 2);
                }

                $deductionFeeRate = bcdiv($interfaceRefund['deduction'], $interfaceRefund['gross_refund'], 2);
                // 供应商采购价
                $supplierAmount = 0;
                $supplierAmount = bcadd($supplierAmount, -$val['ticket_marketing_price'], 2);
                $supplierAmount = bcadd($supplierAmount, -$val['ticket_tax_cn'], 2);
                $supplierAmount = bcadd($supplierAmount, -$val['ticket_tax_yq'], 2);
                $supplierAmount = bcsub($supplierAmount, -$val['ticket_supplier_agency_fee'], 2);
                $supplierAmount = bcadd($supplierAmount, -$val['ticket_supplier_service_fee'], 2);
                $supplierAmount = bcadd($supplierAmount, -$val['insurance_marketing_price'], 2);
                $supplierAmount = bcadd($supplierAmount, $interfaceRefund['deduction'], 2);
                // 销售价
                $customerAmount = 0;
                $customerAmount = bcadd($customerAmount, -$val['ticket_marketing_price'], 2);
                $customerAmount = bcadd($customerAmount, -$val['ticket_tax_cn'], 2);
                $customerAmount = bcadd($customerAmount, -$val['ticket_tax_yq'], 2);
                $customerAmount = bcadd($customerAmount, -$val['ticket_customer_adjust_fee'], 2);
                $customerAmount = bcadd($customerAmount, -$val['ticket_customer_service_fee'], 2);
                $customerAmount = bcadd($customerAmount, -$val['insurance_marketing_price'], 2);
                $customerAmount = bcadd($customerAmount, $interfaceRefund['deduction'], 2);
                // 总的采购价、销售价
                $totalSupplierAmount = bcadd($totalSupplierAmount, $supplierAmount, 2);
                $totalCustomerAmount = bcadd($totalCustomerAmount, $customerAmount, 2);

                $addOrderDetail[] = [
                    'origin_order_type'                  => 2,
                    'origin_order_id'                    => $oldOrderId,
                    'origin_order_detail_id'             => $val['id'],
                    'order_passenger_id'                 => $val['order_passenger_id'],
                    'ticket_supplier_marketing_price'    => -$val['ticket_marketing_price'],
                    'ticket_supplier_tax_cn'             => -$val['ticket_tax_cn'],
                    'ticket_supplier_tax_yq'             => -$val['ticket_tax_yq'],
                    'ticket_supplier_agency_fee'         => -$val['ticket_supplier_agency_fee'],
                    'ticket_supplier_service_fee'        => -$val['ticket_supplier_service_fee'],
                    'ticket_supplier_insurance_fee'      => -$val['insurance_marketing_price'],
                    'ticket_customer_marketing_price'    => -$val['ticket_marketing_price'],
                    'ticket_customer_tax_cn'             => -$val['ticket_tax_cn'],
                    'ticket_customer_tax_yq'             => -$val['ticket_tax_yq'],
                    'ticket_customer_adjust_fee'         => -$val['ticket_customer_adjust_fee'],
                    'ticket_customer_service_fee'        => -$val['ticket_customer_service_fee'],
                    'ticket_customer_insurance_fee'      => -$val['insurance_marketing_price'],
                    'ticket_supplier_deduction_fee'      => $interfaceRefund['deduction'],
                    'ticket_supplier_deduction_fee_rate' => $deductionFeeRate,
                    'ticket_customer_deduction_fee'      => $interfaceRefund['deduction'],
                    'ticket_customer_deduction_fee_rate' => $deductionFeeRate,
                    'supplier_amount'                    => $supplierAmount,//总采购金额
                    'customer_amount'                    => $customerAmount,//总销售金额
                    'origin_price'                       => $interfaceRefund['gross_refund'],
                    'origin_tax_cn'                      => $val['ticket_tax_cn'],
                    'origin_tax_yq'                      => $val['ticket_tax_yq'],
                    'origin_deduction_fee'               => $interfaceRefund['deduction'],
                    'origin_deduction_fee_rate'          => $deductionFeeRate,//退票费率
                ];
            }

            $db->transException(true)->transStart();
            //3.记录数据库
            //3.1 记录order表
            $insertOrderId = $refundOrderModel->insert([
                'order_no'              => $newOrderNo,
                'origin_order_type'     => 1,
                'origin_order_id'       => $oldOrder['id'],
                'origin_order_no'       => $oldOrder['order_no'],
                'refund_type'           => $params['refund_type'],
                'ticket_type'           => 1,
                'order_source'          => 1,
                'area_type'             => TicketBookOrderModel::ORDER_AREA_INTL,
                'customer_type'         => 1,
                'customer_id'           => 0,//客户ID，包括直销会员和分销会员
                'pnr'                   => $isSplitPnr ? $pnr : $oldOrder['pnr'],
                'pnr_id'                => $isSplitPnr ? $pnrId : $oldOrder['pnr_id'],
                'journey_type'          => $oldOrder['journey_type'],//航程类型
                'journey_info'          => $oldOrder['journey_info'],//航程
                'passenger_number'      => $countPassenger,//旅客人数
                'passenger_names'       => $passengerNames,//旅客姓名
                'total_supplier_amount' => $totalSupplierAmount,//总采购金额
                'total_customer_amount' => $totalCustomerAmount,//总销售金额
                'office'                => config('IBE')->office,//OFFICE号
                'status'                => 2,//已完成
                'contact_name'          => $params['contact_name'],//联系人
                'contact_telephone'     => $params['contact_telephone'],//联系电话
                'contact_email'         => $params['contact_email'],//联系邮箱
                'is_send_sms'           => $params['is_send_sms'],
                'remark'                => $params['remark'],
                'operator_id'           => 1,//TODO 因登录前端还没接，先暂时写死
                'operator_name'         => '测试账号',//TODO 因登录前端还没接，先暂时写死
            ]);
            if (empty($insertOrderId)) {
                throw new \Exception('添加订单失败');
            }

            //3.2 记录order_detail、order_passengers
            $passengerIdMap = [];
            foreach ($orderPassengers as $passenger) {
                $passenger['order_id']            = $insertOrderId;
                $passenger['origin_order_type']   = 1;
                $passenger['origin_order_id']     = $oldOrder['id'];
                $passenger['origin_passenger_id'] = $passenger['id'];
                $passenger['status']              = 3;
                $passenger['created_at']          = time();
                $passenger['updated_at']          = time();
                $oldPassengerId                   = $passenger['id'];
                unset($passenger['id']);
                $insertPassengerId               = $refundOrderPassengerModel->insert($passenger);
                $passengerIdMap[$oldPassengerId] = $insertPassengerId;
            }
            foreach ($addOrderDetail as $val) {
                $val['order_id']           = $insertOrderId;
                $val['order_passenger_id'] = $passengerIdMap[$val['order_passenger_id']];;
                $refundOrderDetailModel->insert($val);
            }
            // 3.3写入order_segments
            // TODO 需要指定退哪些航段
            $oldOrderSegments = $orderSegmentModel->where('order_id', $oldOrderId)->findAll();
            foreach ($oldOrderSegments as $orderSegment) {
                $orderSegment['order_id']   = $insertOrderId;
                $orderSegment['created_at'] = time();
                $orderSegment['updated_at'] = time();
                unset($orderSegment['id']);
                $refundOrderSegmentModel->insert($orderSegment);
            }

            // 写入order_passenger_segments
            $oldOrderPassengerSegments = $orderPassengerSegmentModel->whereIn('passenger_id', $passengerIds)->findAll();
            foreach ($oldOrderPassengerSegments as $orderPassengerSegments) {
                $orderPassengerSegments['passenger_id']  = $passengerIdMap[$orderPassengerSegments['passenger_id']];
                $orderPassengerSegments['ticket_status'] = 'REFUNDED';
                $orderPassengerSegments['status']        = 5;
                unset($orderPassengerSegments['id']);
                $refundOrderPassengerSegmentModel->insert($orderPassengerSegments);
            }

            // 如果有分离PNR，则更新order_id到PNR表
            if ($isSplitPnr) {
                $pnrModel->where('id', $pnrId)->set('order_id', $insertOrderId)->update();
            }

            // 更新原有乘客表的状态为已退票
            foreach ($passengerIdMap as $k => $v) {
                $orderPassengerModel->where('id', $k)->where('order_id', $oldOrderId)->set('status', 3)->update();
            }
            // 更新原有乘客航段表的状态为已退票
            foreach ($passengerIdMap as $k => $v) {
                $orderPassengerSegmentModel->where('passenger_id', $k)->where('status', 1)->set(['status' => 5, 'ticket_status' => 'REFUNDED'])->update();
            }

            $db->transComplete();
        } catch (\Exception $e) {
            $db->transRollback();
            log_message('error', "确认退票失败：{$e->getMessage()}");
            error(0, $e->getMessage());
        }

        return [];
    }

    /**
     * 退款订单列表
     *
     * @param  array  $params
     *
     * @return array
     */
    //public function refundList(array $params): array
    //{
    //    $page    = $params['page'] ?? 1;
    //    $perPage = $params['per_page'] ?? 10;
    //
    //    /**
    //     * @var \App\Models\TicketRefundOrderModel $orderModel
    //     */
    //    $orderModel = model('TicketRefundOrderModel');
    //    $orderWhere = $this->handleWhere($params, 'refund');
    //
    //    //分页
    //    $list  = $orderModel->paginate_list($orderWhere, $page, $perPage);
    //    $pager = $orderModel->pager;
    //    //分组
    //    $totalStatus = $orderModel->total_status($orderWhere);
    //
    //    return [
    //        'list'         => $list,
    //        'total_status' => $totalStatus,
    //        'total'        => $pager->getTotal(),//总条数
    //        'perPage'      => $pager->getPerPage(),//每页显示条数
    //        'pageCount'    => $pager->getPageCount(),//总页数
    //        'currentPage'  => $pager->getCurrentPage(),//当前页数
    //    ];
    //}

    /**
     * 退款订单详情
     *
     * @param  int  $orderId
     *
     * @return array
     * @throws \Exception
     */
    public function refundDetail(int $orderId): array
    {
        $refundOrderModel        = model('TicketRefundOrderModel');
        $refundOrderSegmentModel = model('TicketRefundSegModel');

        // 获取退款订单信息
        $order = $refundOrderModel->find($orderId);
        if (empty($order)) {
            error(0, 'order_id错误');
        }

        // 构建订单基础信息
        $orderData = $this->buildOrderData($order, 'refund');

        // 获取航班信息
        $orderSegments = $refundOrderSegmentModel->where('order_id', $orderId)->findAll();
        $flightOut     = $this->buildFlightData($orderSegments, $order['journey_type']);

        // 构建乘客信息
        $passengerData = $this->buildPassengerData($orderId, 'detail', 'TicketRefundPaxModel', 'TicketRefundPaxSegModel');

        // 构建价格信息
        $priceData = $this->buildRefundPriceData($orderId);

        return [
            'order_data'     => $orderData,
            'flight_data'    => $flightOut,
            'passenger_data' => $passengerData,
            'price_data'     => $priceData,
        ];
    }
}