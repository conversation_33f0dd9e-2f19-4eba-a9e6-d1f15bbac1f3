<?php

namespace App\Services\Intl;

use App\Helpers\Tools\Char;
use App\Helpers\Tools\Mobile;
use App\Helpers\Tools\Time;
use App\Models\OrderModel;
use App\Models\OrderPassengerModel;
use App\Models\PnrPassengerModel;
use App\Models\TicketBookOrderModel;
use Exception;

class Order extends IntlBaseService
{
    protected function getOrderModel()
    {
        return model('TicketBookOrderModel');
    }

    protected function getOrderType(): string
    {
        return 'order';
    }

    /**
     * 航段信息组装
     *
     * @param $flightSegments
     * @param $flights
     * @param $cabinsArr
     *
     * @return array
     */
    public function createFlightSegments($flightSegments, $flights, $cabinsArr): array
    {
        $flightSegmentArr = [];
        //航段信息组装
        foreach ($flightSegments as $key => $fs) {
            $rph              = $key + 1;
            $cabin            = $fs['cabin_no'];
            $segmentProductNo = $fs['product_no'];
            $departureDate    = $fs['departure_date'];
            $arrivalDate      = $fs['arrival_date'];
            $flightNumber     = $fs['flight_number'];

            //校验舱位
            $airlineCode = $flights[$flightNumber]['airline_code'];
            $tmpKey      = $airlineCode . $cabin;
            if (!isset($cabinsArr[$tmpKey])) {
                error(0, "未知航司舱位{$tmpKey}");
            }
            $flight = $flights[$flightNumber];
            //校验出发时间
            if ($departureDate < date('Y-m-d')) {
                error(0, "出发时间不允许小于当前日期");
            }

            $flightSegmentArr[] = [
                'rph'                 => $rph,//航段编号
                'departure_datetime'  => $departureDate . 'T' . $flight['departure_time'] . ':00', //出发时间
                'arrival_datetime'    => $arrivalDate . 'T' . $flight['arrival_time'] . ':00', //到达时间
                'code_share_ind'      => false, // 是否共享航班
                'flight_number'       => $flightNumber, //航班号
                'status'              => 'NN', // 行动代码
                'segment_type'        => 'NORMAL', // 航段类型，默认为NORMAL
                'departure_airport'   => $flight['departure_airport'], //出发机场
                'arrival_airport'     => $flight['arrival_airport'], //到达机场
                'air_equip_type'      => $flight['air_equip_type'], //机型
                'marketing_airline'   => $flight['airline_code'],//航空公司字母代码
                'booking_class_avail' => $cabin, // 预订舱位
                'product_no'          => $segmentProductNo, // 单航程产品编号,
            ];
            ////各舱位对应的各类型乘客票价总额
            //if (isset($cabinPriceTotal[$productNo])) {
            //    $cabinPriceTotal[$productNo]['ADT'] += $fs['adult_price'];//成人价
            //    $cabinPriceTotal[$productNo]['CNN'] += $fs['children_price'];//儿童价
            //    $cabinPriceTotal[$productNo]['INF'] += $fs['baby_price'];//婴儿价
            //} else {
            //    $cabinPriceTotal[$productNo]['ADT'] = $fs['adult_price'];//成人价
            //    $cabinPriceTotal[$productNo]['CNN'] = $fs['children_price'];//儿童价
            //    $cabinPriceTotal[$productNo]['INF'] = $fs['baby_price'];//婴儿价
            //}
        }

        return $flightSegmentArr;
    }

    /**
     * 组装乘客信息
     *
     * @param $passengers
     *
     * @return array
     * @throws Exception
     */
    public function createPassengers($passengers): array
    {
        $passengerArr = [];
        $infRphs      = [];//婴儿rphs
        $i            = 1;
        foreach ($passengers as $pg) {
            $rph                = $i;
            $passengerTypeCode  = intval($pg['passenger_type_code']);//乘客类型：1成人 2儿童 3婴儿  对应接口：ADT成人 CNN儿童 INF婴儿
            $certificateType    = intval($pg['certificate_type']);//证件类型：1身份证 2护照  对应接口：NI 身份证 PP 护照
            $certificateNumber  = trim($pg['certificate_number']);//证件号码
            $certificateCountry = trim($pg['certificate_country']);//证件签发国家
            $certificateValid   = trim($pg['certificate_valid']);//证件有效期
            $contactPhone       = trim($pg['contact_phone']);//乘客联系电话
            $infantTravelerRph  = 0;//婴儿关联的成人乘客ID
            $surname            = trim($pg['surname']);//乘客姓
            $givenName          = trim($pg['given_name']);//乘客名

            //验证手机号
            if (!Mobile::validPhone($contactPhone)) {
                error(0, "乘客{$givenName} {$surname}手机号码有误有误_{$contactPhone}");
            }

            //校验年龄
            $age = Time::calculateAge($pg['birthday']);
            switch ($passengerTypeCode) {
                case 1://成人
                    if ($age < 18) {
                        error(0, "乘客序号:{$rph},乘客类型为成人和身份证年龄不符");
                    }
                    break;
                case 2://儿童
                    if (!($age >= 2 and $age <= 17)) {
                        error(0, "乘客序号:{$rph},乘客类型为儿童和身份证年龄不符");
                    }
                    break;
                case 3://婴儿
                    if (!($age < 2)) {
                        error(0, "乘客序号:{$rph},乘客类型为婴儿和身份证年龄不符");
                    }
                    $infRphs[] = $rph;
                    break;
            }

            $passengerArr[] = [
                'rph'                 => $rph,
                'passenger_type_code' => PnrPassengerModel::INTL_PASSENGER_MAP[$passengerTypeCode], //乘客类型：ADT成人 CNN儿童 INF婴儿
                'gender'              => $pg['gender'] == 1 ? 'MALE' : 'FEMALE', //1（男）或 0（女）,
                'age'                 => $age,
                'birthday'            => $pg['birthday'],//出生日期
                'language_type'       => 'ZH',
                'surname'             => $surname,
                'given_name'          => $givenName,
                'nationality'         => $pg['nationality'],//国籍
                'doc'                 => [ //证件
                    'doc_type'    => $certificateType,
                    'doc_id'      => $certificateNumber,
                    'doc_country' => $certificateCountry,
                    'doc_valid'   => $certificateValid,
                ],
                'ctcm'                => $contactPhone,//乘客联系电话
                'infant_traveler_rph' => $infantTravelerRph, // 可选，关联婴儿乘客ID，如果没有关联，则可以提供该字段
                'tc'                  => '',//TC组，可选，可不填或留空
            ];
            $i++;
        }

        //设置携带者rph（成人绑定婴儿rph）
        foreach ($passengerArr as $k => $p) {
            if ($p['passenger_type_code'] == 'ADT' && !empty($infRphs)) {
                $p['infant_traveler_rph'] = array_shift($infRphs);
            }
            $passengerArr[$k] = $p;
        }

        return $passengerArr;
    }

    /**
     * 创建订单
     *
     * @param $reqParams
     * @param $pnrParams
     * @param $cabinPriceTotal
     *
     * @return bool|int|string|void
     */
    public function createOrder($reqParams, $pnrParams, $cabinPriceTotal)
    {
        /**
         * @var QueryPrice $queryPrice
         */
        $queryPrice                    = load_service('Intl\QueryPrice');
        $booking                       = new \App\Libraries\Api\IBE\Booking();
        $pnrModel                      = model('PnrModel');
        $ticketBookOrderDetailModel    = model('TicketBookOrderDetailModel');
        $ticketBookOrderPassengerModel = model('TicketBookPaxModel');
        $ticketBookOrderSegmentModel   = model('TicketBookSegModel');
        $ticketBookOrderModel          = model('TicketBookOrderModel');

        //出票时限
        $ticketTimeLimit  = $pnrParams['ticket_time_limit'];
        $ticketTimeLimits = explode('T', $ticketTimeLimit);
        $ticketTimeLimit  = $ticketTimeLimits[0] . ' ' . $ticketTimeLimits[1];
        $countPassenger   = count($pnrParams['passengers']);//旅客人数

        // 产品编号
        $productNo = implode('+', array_column($pnrParams['flight_segments'], 'product_no'));

        $db = \Config\Database::connect();
        try {
            //创建PNR
            $pnr = $booking->create_pnr($pnrParams);
            if (!empty($pnrModel->where('pnr', $pnr)->first())) {
                throw new Exception('PNR已存在，请勿重复生成');
            }

            $db->transStart();

            //1.添加pnr
            $insertPnrId = $pnrModel->insert([
                'pnr'               => $pnr,
                'ticket_time_limit' => $ticketTimeLimit,
                'passenger_number'  => $countPassenger,
                'contact_name'      => $reqParams['contact_name'],
                'contact_telephone' => $reqParams['contact_telephone'],
            ]);
            if (empty($insertPnrId)) {
                throw new Exception('添加PNR表失败');
            }

            //2.根据PNR编号查询国际机票价格
            //几种乘客类型就调几次, 从返回值找到对应的产品进行比对价格
            $passengerTypeCodes = array_unique(array_column($pnrParams['passengers'], 'passenger_type_code'));
            $storePrices        = [];//各类型旅客全程的单价
            $fareBasisCodes     = [];//接口返回的所有产品id

            $passengerTypeCount = []; // 各旅客类型对应的人数
            foreach ($pnrParams['passengers'] as $p) {
                if (!isset($passengerTypeCount[$p['passenger_type_code']])) {
                    $passengerTypeCount[$p['passenger_type_code']] = 1;
                } else {
                    $passengerTypeCount[$p['passenger_type_code']]++;
                }

            }

            $passengerParams = [];
            foreach ($passengerTypeCodes as $code) {
                $passengerParams[] = [
                    'type'  => $code,
                    'count' => $passengerTypeCount[$code],
                ];
            }

            // IBE通过pnr获取价格
            $pnrPrices = $queryPrice->queryPriceByPNR(['passengers' => $passengerParams, 'pnr_id' => $pnr]);
            foreach ($pnrPrices as $p) {
                $passengerType    = $p['passenger_type'];
                $fareBasisCode    = $p['fare_basis_code']; //产品编号
                $fareBasisCodes[] = $fareBasisCode;
                $pageTotalPrice   = $cabinPriceTotal[$passengerType];
                if ($pageTotalPrice < $p['after_tax_price']) {
                    throw new Exception("PNR_{$pnr},产品编号:{$fareBasisCode},乘客类型:{$passengerType},页面价格小于接口返回的价格：页面价格:{$pageTotalPrice},接口返回的价格:{$p['after_tax_price']}");
                }
                $tmpTaxes      = array_column($p['taxes'], null, 'tax_code');
                $storePrices[] = [
                    'passenger_type'  => $passengerType,
                    'passenger_rph'   => 0,
                    'payment_type'    => 'CASH',
                    'pnr'             => $pnr,
                    //'fare_ref_rph'    => $p['rph'],
                    'price'           => $p['after_tax_price'],//价格
                    'taxes'           => $tmpTaxes,//税费
                    'fare_basis_code' => $fareBasisCode,//产品编号
                ];
            }
            if (empty($storePrices)) {
                throw new Exception("根据PNR编号查询国际机票价格失败, 提交上来的产品：{$productNo}，接口返回的产品：" . implode(',', $fareBasisCodes));
            }

            // 3.把价格存储到PNR（国际的不需要）
            // 4.添加机票订单表
            //航程(格式是多个航段的出发到达机场代码拼接，如CANWUH)
            $journeyInfo = '';
            foreach ($pnrParams['flight_segments'] as $fs) {
                $journeyInfo .= $fs['departure_airport'] . $fs['arrival_airport'];
            }
            //旅客姓名(格式是旅客姓名用逗号拼接,如张三,李四)
            $passengerNames = implode(',', array_column($pnrParams['passengers'], 'surname'));
            //采购价总额(各类型乘客的价格总额)
            $totalSupplierPrice = 0;
            //总采购金额(采购价总额+税费总额)
            $totalSupplierAmount = 0;
            //各税费总额
            $totalTax    = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];
            $storePrices = array_column($storePrices, null, 'passenger_type');
            foreach ($pnrParams['passengers'] as $pa) {
                $passengerTypeCode  = $pa['passenger_type_code'];
                $unitPrice          = $storePrices[$passengerTypeCode]['price'];
                $taxes              = $storePrices[$passengerTypeCode]['taxes'];
                $totalSupplierPrice = bcadd($totalSupplierPrice, $unitPrice, 2);
                foreach ($taxes as $key => $val) {
                    $totalTax[$key]      = bcadd($totalTax[$key], $val['amount'], 2);
                    $totalSupplierAmount = bcadd($totalSupplierAmount, $val['amount'], 2);
                }
                $totalSupplierAmount = bcadd($totalSupplierAmount, $totalSupplierPrice, 2);
            }

            $orderNo       = $ticketBookOrderModel->generate_order_no();//订单号
            $insertOrderId = $ticketBookOrderModel->insert([
                'order_no'              => $orderNo,
                'ticket_type'           => 1,
                'order_source'          => 1,
                'area_type'             => TicketBookOrderModel::ORDER_AREA_INTL,
                'customer_type'         => 1,
                'customer_id'           => 0,//客户ID，包括直销会员和分销会员
                'pnr'                   => $pnr,
                'pnr_id'                => $insertPnrId,
                'journey_type'          => $reqParams['flight_type'],//航程类型
                'journey_info'          => $journeyInfo,//航程
                'passenger_number'      => $countPassenger,//旅客人数
                'passenger_names'       => $passengerNames,//旅客姓名
                'contact_name'          => $reqParams['contact_name'],//联系人
                'contact_telephone'     => $reqParams['contact_telephone'],//联系电话
                'total_supplier_amount' => $totalSupplierAmount,//总采购金额
                'total_customer_amount' => $totalSupplierAmount,//总销售金额
                'office'                => config('IBE')->office,//OFFICE号
                'status'                => 1,//已订座
                'operator_id'           => 1,//TODO 因登录前端还没接，先暂时写死
                'operator_name'         => '测试账号',//TODO 因登录前端还没接，先暂时写死
            ]);
            if (empty($insertOrderId)) {
                throw new Exception('添加订单表失败');
            }

            // 5.添加订单航段表
            foreach ($pnrParams['flight_segments'] as $fs) {
                // 出发时间
                $departureDatetime = str_replace('T', ' ', $fs['departure_datetime']);
                // 到达时间
                $arrivalDatetime = str_replace('T', ' ', $fs['arrival_datetime']);
                //代码共享
                $codeShareInd = $fs['code_share_ind'] ? 1 : 0;

                $insertOrderSegmentId = $ticketBookOrderSegmentModel->insert([
                    'order_id'                => $insertOrderId,
                    'rph'                     => $fs['rph'],
                    'departure_datetime'      => $departureDatetime,//出发时间
                    'arrival_datetime'        => $arrivalDatetime,//到达时间
                    'code_share_ind'          => $codeShareInd,//代码共享
                    'airline'                 => $fs['marketing_airline'],//市场方航空公司
                    'flight_number'           => $fs['flight_number'],//市场方航班号
                    'operating_airline'       => $fs['marketing_airline'],//承运方航空公司
                    'operating_flight_number' => $fs['flight_number'],//承运方航班号
                    'cabin'                   => $fs['booking_class_avail'],//舱位
                    'sub_cabin'               => $fs['product_no'],//产品
                    'fbc'                     => $fs['product_no'],//产品
                    'passenger_number'        => $countPassenger,//旅客人数
                    'action_code'             => $fs['status'],//行动代码
                    'segment_type'            => $fs['segment_type'],//航段类型
                ]);
            }

            //6.添加订单乘客表
            $addOrderDetail = [];//订单明细表数据
            foreach ($pnrParams['passengers'] as $p) {
                $insertOrderPassengerId = $ticketBookOrderPassengerModel->insert([
                    'order_id'               => $insertOrderId,
                    'rph'                    => $p['rph'],
                    'passenger_type'         => PnrPassengerModel::INTL_PASSENGER_MAP_2[$p['passenger_type_code']],
                    'doc_type'               => $p['doc']['doc_type'],
                    'doc_id'                 => $p['doc']['doc_id'],
                    'doc_issue_country'      => $p['doc']['doc_country'],
                    'expire_date'            => $p['doc']['doc_valid'],
                    'doc_holder_nationality' => $p['nationality'],
                    'doc_holder_ind'         => 1,
                    'person_name'            => $p['surname'] . $p['given_name'],
                    'surname'                => $p['surname'],
                    'given_name'             => $p['given_name'],
                    'passenger_age'          => $p['age'],
                    'language_type'          => 'ZH',
                    'telephone'              => $p['ctcm'],
                    'birthday'               => $p['birthday'],
                    'status'                 => 0,
                    'gender'                 => PnrPassengerModel::GENDER_MAP[$p['gender']],
                ]);

                $addOrderDetail[] = [
                    'order_passenger_id' => $insertOrderPassengerId,
                    'passenger_type'     => PnrPassengerModel::INTL_PASSENGER_MAP_2[$p['passenger_type_code']],
                ];
            }

            //7.添加订单明细表
            foreach ($addOrderDetail as $od) {
                $passengerType    = PnrPassengerModel::INTL_PASSENGER_MAP[$od['passenger_type']];//旅客类型
                $orderPassengerId = $od['order_passenger_id'];//订单乘客ID
                foreach ($storePrices as $sp) {
                    if ($sp['passenger_type'] == $passengerType) {
                        //采购总金额(采购价+税费)
                        $totalTax       = 0;
                        $totalTaxDetail = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];
                        foreach ($sp['taxes'] as $key => $val) {
                            $totalTax             = bcadd($totalTax, $val['amount'], 2);
                            $totalTaxDetail[$key] = $val['amount'];
                        }
                        $supplierAmount      = bcadd($sp['price'], $totalTax, 2);//总采购金额
                        $insertOrderDetailId = $ticketBookOrderDetailModel->insert([
                            'order_id'               => $insertOrderId,
                            'order_passenger_id'     => $orderPassengerId,
                            'product_type'           => 1,
                            'product_id'             => $insertPnrId,
                            'supplier_id'            => 0,
                            'customer_id'            => 0,
                            'supplier_amount'        => $supplierAmount,//总采购金额
                            'customer_amount'        => $supplierAmount,//总销售金额
                            'ticket_marketing_price' => $sp['price'],//市场价/账单价/票面价
                            'ticket_tax_cn'          => $totalTaxDetail['CN'],//机场建设费
                            'ticket_tax_yq'          => $totalTaxDetail['YQ'],//燃油附加费
                            'ticket_tax_xt'          => $totalTaxDetail['XT'],//其他税费
                            'ticket_total_price'     => $supplierAmount,//票面总价
                            'ticket_supplier_price'  => $sp['price'],//采购价
                            'origin_marketing_price' => $sp['price'],
                            'origin_tax_cn'          => $totalTaxDetail['CN'],
                            'origin_tax_yq'          => $totalTaxDetail['YQ'],
                            'origin_tax_xt'          => $totalTaxDetail['XT'],
                        ]);
                    }
                }
            }

            //8.回写PNR系列表的order_id
            $pnrModel->where('id', $insertPnrId)->set('order_id', $insertOrderId)->update();
            $db->transComplete();

            return $insertOrderId;
        } catch (Exception $e) {
            //TODO 记录错误日志
            $db->transRollback();
            error(0, $e->getMessage());
        }
    }

    /**
     * 确认订单
     *
     * @param $orderId
     *
     * @return array|void
     * @throws \Exception
     */
    public function confirmOrder($orderId)
    {
        /**
         * @var \App\Services\Intl\Shopping $shopping
         */
        $shopping            = load_service('Intl\Shopping');
        $airportModel        = model('AirportModel');
        $airlineModel        = model('AirlineModel');
        $flightModel         = model('FlightModel');
        $cabinModel          = model('CabinModel');
        $pnrModel            = model('PnrModel');
        $orderSegmentModel   = model('TicketBookSegModel');
        $orderModel          = model('TicketBookOrderModel');
        $orderDetailModel    = model('TicketBookOrderDetailModel');
        $orderPassengerModel = model('TicketBookPaxModel');

        //订单信息
        $order = $orderModel->find($orderId);
        if (empty($order)) {
            error(0, 'order_id错误');
        }

        //机场、城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');
        //航空公司
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->findAll();
        $airlines = array_column($airlines, null, 'airline_code');
        //航班信息
        $orderSegments = $orderSegmentModel->where('order_id', $orderId)->findAll();
        $flights       = $flightModel->whereIn('flight_number', array_column($orderSegments, 'flight_number'))->findAll();//航班信息
        $flights       = array_column($flights, null, 'flight_number');

        $pnr       = $pnrModel->find($order['pnr_id']);
        $orderData = [
            'order_no'          => $order['order_no'],//订单编号
            'pnr'               => $order['pnr'],
            'total_amount'      => $order['total_customer_amount'],//订单总额(总销售额)
            'ticket_time_limit' => $pnr['ticket_time_limit'] . '前',//出票时限
            'status'            => $order['status'],
            'status_text'       => OrderModel::ORDER_STATUS[$order['status']],
            'journey_type'      => $order['journey_type'],
            'journey_type_text' => OrderModel::JOURNEY_TYPES[$order['journey_type']],//航程类型
            'contact_name'      => $order['contact_name'],//联系人
            'contact_telephone' => $order['contact_telephone'],//联系电话
        ];

        // 计算航程名称 (与confirmOrder相同逻辑)
        $journeyNames = $this->generateJourneyNames($order['journey_type'], $orderSegments);

        $flightData    = [];
        $totalDuration = 0;
        foreach ($orderSegments as $index => $orderSegment) {
            $flight      = $flights[$orderSegment['flight_number']];
            $airlineCode = $flight['airline_code']; // 航司2位编号

            // 出发/到达时间 间隔
            $interval = Time::compute_interval($flight['departure_time'], $flight['arrival_time']);
            $hours    = $interval['hours'] ? $interval['hours'] . 'h' : '';
            $minutes  = $interval['minutes'] ? $interval['minutes'] . 'm' : '';

            //航司logo
            $airlineLogo = "/static/images/airline/{$flight['airline_code']}.png";

            //折扣、行李重量
            $cabin = $cabinModel->where(['airline' => $airlineCode, 'cabin' => $orderSegment['cabin']])->first();

            // 起飞和到达时间
            $departureDatetime = new \CodeIgniter\I18n\Time($orderSegment['departure_datetime']);
            $arrivalDatetime   = new \CodeIgniter\I18n\Time($orderSegment['arrival_datetime']);
            $duration          = $departureDatetime->difference($arrivalDatetime)->getMinutes();
            $totalDuration     += $duration;

            $flightData[] = [
                'journey_name'         => $journeyNames[$index] ?? '未知',
                'airline_code'         => $airlineCode,//航司编号2位字母
                'airline_logo'         => $airlineLogo,
                'flight_number'        => $orderSegment['flight_number'],
                'airline_cn'           => $airlines[$airlineCode]['airline_cn'],//航空公司名称
                'air_equip_type'       => $flight['air_equip_type'], //机型
                'departure_airport'    => $airports[$flight['departure_airport']]['airport_code'],
                'departure_airport_cn' => $airports[$flight['departure_airport']]['city_cn'] . $airports[$flight['departure_airport']]['airport_name_cn'],//城市+出发机场名称
                'arrival_airport'      => $airports[$flight['arrival_airport']]['airport_code'],
                'arrival_airport_cn'   => $airports[$flight['arrival_airport']]['city_cn'] . $airports[$flight['arrival_airport']]['airport_name_cn'],//城市+到达机场名称
                'departure_date'       => $departureDatetime->toDateString(),
                'departure_time'       => $departureDatetime->format('H:i'),
                'arrival_date'         => $arrivalDatetime->toDateString(),
                'arrival_time'         => $arrivalDatetime->format('H:i'),
                'interval'             => $hours . $minutes,
                'duration'             => $duration,
                'cabin_no'             => $orderSegment['cabin'], //舱位编号
                'discount'             => $cabin['discount'] . '折',//折扣
                'luggage_weight'       => $cabin['luggage_weight'],//行李重量
                'meal'                 => '有餐食',//TODO:需要计算出来，此处暂写死
                'refund_rate'          => '退票10%-45%', //退票范围 TODO:需要计算出来，此处暂写死
                'sub_cabin'            => $orderSegment['sub_cabin'],//产品编号
            ];
        }

        // 处理航段间的中转信息
        $flightData = $shopping->handleSegmentTransit($flightData, $airports);
        // 处理外层所需的一些航班信息
        // 起飞和落地信息
        $firstFlight   = $flightData[0];
        $lastFlight    = $flightData[count($flightData) - 1];
        $totalInterval = Time::computeHoursByDuration($totalDuration);
        $flightOut     = [
            'airline_code'         => $flightData[0]['airline_code'],
            'flight_number'        => $flightData[0]['flight_number'],
            'air_equip_type'       => $flightData[0]['air_equip_type'],
            'departure_airport'    => $firstFlight['departure_airport'],
            'departure_airport_cn' => $firstFlight['departure_airport_cn'],
            'departure_date'       => $firstFlight['departure_date'],
            'departure_time'       => $firstFlight['departure_time'],
            'arrival_airport'      => $lastFlight['arrival_airport'],
            'arrival_airport_cn'   => $lastFlight['arrival_airport_cn'],
            'arrival_date'         => $lastFlight['arrival_date'],
            'arrival_time'         => $lastFlight['arrival_time'],
            'duration'             => $totalDuration,
            'interval'             => $totalInterval['hours'] . 'h' . $totalInterval['minutes'] . 'm',
            'is_direct'            => count($flightData) == 1,
            'list'                 => $flightData,
        ];

        // 乘机人信息
        $passengerData   = [];
        $orderPassengers = $orderPassengerModel->where('order_id', $orderId)->findAll();
        foreach ($orderPassengers as $pp) {
            $passengerData[] = [
                'rph'                    => $pp['rph'],//序号
                'person_name'            => $pp['person_name'],//乘客姓名
                'surname'                => $pp['surname'],
                'given_name'             => $pp['given_name'],
                'birthday'               => $pp['birthday'],
                'passenger_age'          => $pp['passenger_age'],//乘客年龄
                'gender'                 => $pp['gender'],//乘客性别
                'passenger_type'         => $pp['passenger_type'],//乘客类型
                'passenger_type_text'    => PnrPassengerModel::PASSENGER_TYPE[$pp['passenger_type']],//乘客类型文案
                'doc_type'               => $pp['doc_type'],//证件类型
                'doc_type_text'          => PnrPassengerModel::DOC_TYPE[$pp['doc_type']],//证件类型文案
                'doc_id'                 => $pp['doc_id'],//证件号码
                'doc_issue_country'      => $pp['doc_issue_country'],
                'doc_valid'              => $pp['expire_date'],//证件有效期
                'doc_holder_nationality' => $pp['doc_holder_nationality'],
                'telephone'              => Char::mask_phone($pp['telephone']),//联系电话
            ];
        }

        //价格信息
        $orderDetails    = $orderDetailModel->where('order_id', $orderId)->findAll();
        $orderPassengers = array_column($orderPassengers, null, 'id');

        $passengerDetails = [];
        foreach ($orderDetails as $orderDetail) {
            $passengerType                      = $orderPassengers[$orderDetail['order_passenger_id']]['passenger_type'];
            $passengerDetails[$passengerType][] = $orderDetail;
        }
        $priceData = [
            'detail' => [],
            'total'  => [
                'total_customer_price'  => 0.00,
                'total_tax_cn'          => 0.00,
                'total_tax_yq'          => 0.00,
                'total_tax_xt'          => 0.00,
                'total_customer_amount' => 0.00,
            ],
        ];

        foreach ($passengerDetails as $key => $val) {
            $item              = $val[0];
            $passengerType     = $key;
            $passengerTypeText = OrderPassengerModel::PASSENGER_TYPE[$passengerType];
            $passengerNum      = count($val);//该类型的人数
            $customerPrice     = $item['ticket_marketing_price'] * $passengerNum;//销售价
            $taxCn             = $item['ticket_tax_cn'] * $passengerNum;//机建机场建设费
            $taxYq             = $item['ticket_tax_yq'] * $passengerNum; //燃油附加费
            $taxXt             = $item['ticket_tax_xt'] * $passengerNum;//其他税费
            $customerAmount    = $item['customer_amount'] * $passengerNum;//总销售金额

            $priceData['detail'][]                       = [
                'name'            => $passengerTypeText . ' * ' . $passengerNum,
                'customer_price'  => $customerPrice,
                'tax_cn'          => $taxCn,
                'tax_yq'          => $taxYq,
                'tax_xt'          => $taxXt,
                'customer_amount' => $customerAmount,
            ];
            $priceData['total']['total_customer_price']  = bcadd($priceData['total']['total_customer_price'], $customerPrice, 2);
            $priceData['total']['total_tax_cn']          = bcadd($priceData['total']['total_tax_cn'], $taxCn, 2);
            $priceData['total']['total_tax_yq']          = bcadd($priceData['total']['total_tax_yq'], $taxYq, 2);
            $priceData['total']['total_tax_xt']          = bcadd($priceData['total']['total_tax_xt'], $taxXt, 2);
            $priceData['total']['total_customer_amount'] = bcadd($priceData['total']['total_customer_amount'], $customerAmount, 2);
        }

        return [
            'order_data'     => $orderData,
            'flight_data'    => $flightOut,
            'passenger_data' => $passengerData,
            'price_data'     => $priceData,
        ];
    }

    /**
     * 订单详情
     *
     * @param  int  $orderId  订单ID
     *
     * @return array
     * @throws \Exception
     */
    public function orderDetail(int $orderId): array
    {
        $orderModel        = model('TicketBookOrderModel');
        $orderSegmentModel = model('TicketBookSegModel');

        // 获取订单信息
        $order = $orderModel->where('id', $orderId)->where('area_type', TicketBookOrderModel::ORDER_AREA_INTL)->first();
        if (empty($order)) {
            error(0, 'order_id错误');
        }

        // 构建订单基础信息
        $orderData = $this->buildOrderData($order, 'order');

        // 获取航班信息
        $orderSegments = $orderSegmentModel->where('order_id', $orderId)->findAll();
        $flightOut     = $this->buildFlightData($orderSegments, $order['journey_type']);

        // 构建乘客信息
        $passengerData = $this->buildOrderDetailPassengerData($orderId);

        return [
            'order_data'     => $orderData,
            'flight_data'    => $flightOut,
            'passenger_data' => $passengerData,
        ];
    }

    /**
     * 保存国际机票订单价格
     *
     * @param  array  $params
     *
     * @return void
     */
    public function savePrice(array $params): void
    {
        $orderModel          = model('TicketBookOrderModel');
        $orderPassengerModel = model('TicketBookPaxModel');
        $orderDetailModel    = model('TicketBookOrderDetailModel');

        // 获取订单信息
        $orderId = intval($params['order_id']);
        if (empty($orderId)) {
            error(0, 'order_id错误');
        }
        $order = $orderModel->find($orderId);
        if (empty($order)) {
            error(0, '未找到订单');
        }
        if ($order['customer_payment_flag'] != 0) {
            error(0, '只有未付款订单才能修改价格');
        }

        // 获取乘客信息
        $inputPassengers   = $params['passengers'];
        $orderPassengers   = $orderPassengerModel->where('order_id', $orderId)->findAll();
        $inputPassengerIds = array_column($params['passengers'], 'passenger_id');
        $dbPassengerIds    = array_column($orderPassengers, 'id');
        // 比较输入的passenger_id和数据库的passenger_id，确保提交的数据是有效的
        if (!empty(array_diff($inputPassengerIds, $dbPassengerIds)) || !empty(array_diff($dbPassengerIds, $inputPassengerIds))) {
            error(0, '数据错误');
        }

        try {
            $this->db->transException(true)->transStart();

            // 更新order_detail
            $totalSupplierAmount = 0;
            $totalCustomerAmount = 0;
            foreach ($inputPassengers as $inputPassenger) {
                $passengerId              = $inputPassenger['passenger_id'];
                $isFree                   = $inputPassenger['is_free'];
                $ticketMarketingPrice     = $inputPassenger['ticket_marketing_price'];
                $ticketTaxCn              = $inputPassenger['ticket_tax_cn'];
                $ticketTaxYq              = $inputPassenger['ticket_tax_yq'];
                $ticketSupplierAgencyFee  = $inputPassenger['ticket_supplier_agency_fee'];
                $ticketSupplierServiceFee = $inputPassenger['ticket_supplier_service_fee'];
                $ticketCustomerAdjustFee  = $inputPassenger['ticket_customer_adjust_fee'];
                $ticketCustomerServiceFee = $inputPassenger['ticket_customer_service_fee'];

                $ticketTotalPrice = 0;
                $ticketTotalPrice = bcadd($ticketTotalPrice, $ticketMarketingPrice, 2);
                $ticketTotalPrice = bcadd($ticketTotalPrice, $ticketTaxCn, 2);
                $ticketTotalPrice = bcadd($ticketTotalPrice, $ticketTaxYq, 2);

                $supplierAmount = 0;
                $supplierAmount = bcadd($supplierAmount, $ticketTotalPrice, 2);
                $supplierAmount = bcsub($supplierAmount, $ticketSupplierAgencyFee, 2);
                $supplierAmount = bcadd($supplierAmount, $ticketSupplierServiceFee, 2);

                $customerAmount = 0;
                $customerAmount = bcadd($customerAmount, $ticketTotalPrice, 2);
                $customerAmount = bcadd($customerAmount, $ticketCustomerAdjustFee, 2);
                $customerAmount = bcadd($customerAmount, $ticketCustomerServiceFee, 2);

                $totalSupplierAmount += (float)$supplierAmount;
                $totalCustomerAmount += (float)$customerAmount;
                $orderDetailArr      = [
                    'is_free'                     => $isFree,
                    'ticket_marketing_price'      => $ticketMarketingPrice,
                    'ticket_tax_cn'               => $ticketTaxCn,
                    'ticket_tax_yq'               => $ticketTaxYq,
                    'ticket_supplier_agency_fee'  => $ticketSupplierAgencyFee,
                    'ticket_supplier_service_fee' => $ticketSupplierServiceFee,
                    'ticket_customer_adjust_fee'  => $ticketCustomerAdjustFee,
                    'ticket_customer_service_fee' => $ticketCustomerServiceFee,
                    'ticket_total_price'          => $ticketTotalPrice,
                    'supplier_amount'             => $supplierAmount,
                    'customer_amount'             => $customerAmount,
                ];
                $orderDetailModel->where('order_passenger_id', $passengerId)
                                 ->where('order_id', $orderId)
                                 ->set($orderDetailArr)
                                 ->update();
                $orderPassengerModel->where('id', $passengerId)
                                    ->where('order_id', $orderId)
                                    ->set(['total_amount' => $ticketTotalPrice])
                                    ->update();
            }
            $orderModel->where('id', $orderId)
                       ->set([
                           'total_supplier_amount' => $totalSupplierAmount,
                           'total_customer_amount' => $totalCustomerAmount,
                       ])
                       ->update();
            $this->db->transComplete();
        } catch (\Exception $e) {
            $this->db->transRollback();
            log_message('error', "[国际机票]保存订单价格失败：{$e->getMessage()}");
            error(0, $e->getMessage());
        }
    }
}