<?php

namespace App\Services\Order;

use App\Services\BaseService;

class Manual extends BaseService
{
    /**
     * 处理手动出票业务逻辑
     *
     * @param  array  $data
     *
     * @return void
     * @throws \Exception
     */
    public function processManualIssue(array $data): void
    {
        $db = db_connect();

        // 加载模型
        $ticketBookOrderModel       = model('TicketBookOrderModel');
        $ticketBookPaxModel         = model('TicketBookPaxModel');
        $ticketBookSegModel         = model('TicketBookSegModel');
        $ticketBookPaxSegModel      = model('TicketBookPaxSegModel');
        $ticketBookOrderDetailModel = model('TicketBookOrderDetailModel');
        $flightModel                = model('FlightModel');
        $airportModel               = model('AirportModel');
        $airlineModel               = model('AirlineModel');
        $pnrModel                   = model('PnrModel');

        try {
            $db->transException(true)->transStart();

            // 1. 数据查询和验证
            $this->validateAndEnrichData($data, $flightModel, $airportModel, $airlineModel);

            // 2. 生成订单号和PNR
            $orderNo = $ticketBookOrderModel->generate_order_no('T');
            $pnr     = $data['pnr'] ?? $this->generatePnr();

            // 3. 计算订单基本信息
            $orderInfo = $this->calculateOrderInfo($data, $orderNo, $pnr);

            // 4. 保存PNR信息
            $pnrId = $this->savePnrInfo($pnrModel, $pnr, $data);

            // 5. 保存订单主表
            $orderId = $this->saveMainOrder($ticketBookOrderModel, $orderInfo, $pnrId);

            // 6. 保存乘客信息
            $passengerIds = $this->savePassengers($ticketBookPaxModel, $orderId, $data['passengers']);

            // 7. 保存航段信息
            $segmentIds = $this->saveSegments($ticketBookSegModel, $orderId, $data['segments']);

            // 8. 保存乘客-航段关联信息
            $this->savePassengerSegments($ticketBookPaxSegModel, $passengerIds, $segmentIds, $data);

            // 9. 保存订单明细信息
            $this->saveOrderDetails($ticketBookOrderDetailModel, $orderId, $passengerIds, $data['passengers']);

            $db->transComplete();

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * 验证并补全数据
     *
     * @param  array  $data
     * @param  object  $flightModel
     * @param  object  $airportModel
     * @param  object  $airlineModel
     *
     * @return void
     * @throws \Exception
     */
    private function validateAndEnrichData(array &$data, $flightModel, $airportModel, $airlineModel): void
    {
        // 验证航班信息并补全数据
        $flightNumbers = array_column($data['segments'], 'flight_number');
        $flights       = $flightModel->whereIn('flight_number', $flightNumbers)->findAll();
        $flightMap     = array_column($flights, null, 'flight_number');

        // 获取机场信息
        $airportCodes = [];
        foreach ($data['segments'] as $segment) {
            $airportCodes[] = $segment['departure_airport'];
            $airportCodes[] = $segment['arrival_airport'];
        }
        $airports   = $airportModel->whereIn('airport_code', array_unique($airportCodes))->findAll();
        $airportMap = array_column($airports, null, 'airport_code');

        // 验证并补全航段信息
        foreach ($data['segments'] as &$segment) {
            $flightNumber = $segment['flight_number'];

            // 如果数据库中有航班信息，使用数据库数据补全
            if (isset($flightMap[$flightNumber])) {
                $flight                   = $flightMap[$flightNumber];
                $segment['airline_code']  = $flight['airline_code'];
                $segment['aircraft_type'] = $segment['air_equip_type'] ?? $flight['aircraft_type'];

                // 验证机场代码是否匹配
                if (!empty($flight['departure_airport']) && $flight['departure_airport'] !== $segment['departure_airport']) {
                    throw new \Exception("航班 {$flightNumber} 的出发机场不匹配");
                }
                if (!empty($flight['arrival_airport']) && $flight['arrival_airport'] !== $segment['arrival_airport']) {
                    throw new \Exception("航班 {$flightNumber} 的到达机场不匹配");
                }
            } else {
                // 从航班号提取航司代码
                $segment['airline_code'] = substr($flightNumber, 0, 2);
            }

            // 验证机场代码
            if (!isset($airportMap[$segment['departure_airport']])) {
                throw new \Exception("未知的出发机场代码: {$segment['departure_airport']}");
            }
            if (!isset($airportMap[$segment['arrival_airport']])) {
                throw new \Exception("未知的到达机场代码: {$segment['arrival_airport']}");
            }
        }

        // 验证乘客信息
        foreach ($data['passengers'] as $passenger) {
            if (empty($passenger['person_name'])) {
                throw new \Exception("乘客姓名不能为空");
            }
            if (empty($passenger['id_number'])) {
                throw new \Exception("乘客证件号不能为空");
            }
        }
    }

    /**
     * 生成PNR
     *
     * @return string
     */
    private function generatePnr(): string
    {
        // 生成6位随机PNR
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $pnr        = '';
        for ($i = 0; $i < 6; $i++) {
            $pnr .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $pnr;
    }

    /**
     * 计算订单基本信息
     *
     * @param  array  $data
     * @param  string  $orderNo
     * @param  string  $pnr
     *
     * @return array
     */
    private function calculateOrderInfo(array $data, string $orderNo, string $pnr): array
    {
        $passengerCount = count($data['passengers']);
        $segmentCount   = count($data['segments']);

        // 计算航程类型
        $journeyType = 1; // 默认单程
        if ($segmentCount == 2) {
            // 检查是否为往返
            $firstSeg  = $data['segments'][0];
            $secondSeg = $data['segments'][1];
            if ($firstSeg['departure_airport'] === $secondSeg['arrival_airport'] &&
                $firstSeg['arrival_airport'] === $secondSeg['departure_airport']) {
                $journeyType = 2; // 往返
            } else {
                $journeyType = 3; // 联程-两航段
            }
        } elseif ($segmentCount > 2) {
            $journeyType = 5; // 多航段
        }

        // 构建航程信息
        $journeyInfo = '';
        foreach ($data['segments'] as $segment) {
            $journeyInfo .= $segment['departure_airport'] . $segment['arrival_airport'];
        }

        // 计算总金额
        $totalAmount = 0;
        foreach ($data['passengers'] as $passenger) {
            $totalAmount += $passenger['ticket_price'];
            $totalAmount += ($passenger['tax_cn'] ?? 0);
            $totalAmount += ($passenger['tax_yq'] ?? 0);
            $totalAmount += ($passenger['tax_xt'] ?? 0);
        }

        // 构建乘客姓名
        $passengerNames = implode(',', array_column($data['passengers'], 'person_name'));

        return [
            'order_no'          => $orderNo,
            'pnr'               => $pnr,
            'journey_type'      => $journeyType,
            'journey_info'      => $journeyInfo,
            'passenger_number'  => $passengerCount,
            'passenger_names'   => $passengerNames,
            'total_amount'      => $totalAmount,
            'contact_name'      => $data['contact_name'],
            'contact_telephone' => $data['contact_telephone'],
            'contact_email'     => $data['contact_email'] ?? '',
            'area_type'         => $data['area_type'] ?? 1, // 默认国内
        ];
    }

    /**
     * 保存PNR信息
     *
     * @param  object  $pnrModel
     * @param  string  $pnr
     * @param  array  $data
     *
     * @return int
     */
    private function savePnrInfo($pnrModel, string $pnr, array $data): int
    {
        return $pnrModel->insert([
            'pnr'               => $pnr,
            'passenger_number'  => count($data['passengers']),
            'contact_name'      => $data['contact_name'],
            'contact_telephone' => $data['contact_telephone'],
            'status'            => 0,
            'created_at'        => time(),
            'updated_at'        => time(),
        ]);
    }

    /**
     * 保存订单主表
     *
     * @param  object  $ticketBookOrderModel
     * @param  array  $orderInfo
     * @param  int  $pnrId
     *
     * @return int
     */
    private function saveMainOrder($ticketBookOrderModel, array $orderInfo, int $pnrId): int
    {
        return $ticketBookOrderModel->insert([
            'order_no'              => $orderInfo['order_no'],
            'order_type'            => 1, // 出票订单
            'ticket_type'           => 1, // 1.BSP 2.GP 3.OP
            'bsp_payment_type'      => 1,
            'order_source'          => 6, // 手动录入
            'area_type'             => $orderInfo['area_type'],
            'customer_type'         => 1, // 自有客户
            'customer_id'           => 0,
            'pnr'                   => $orderInfo['pnr'],
            'pnr_id'                => $pnrId,
            'journey_type'          => $orderInfo['journey_type'],
            'journey_info'          => $orderInfo['journey_info'],
            'passenger_number'      => $orderInfo['passenger_number'],
            'passenger_names'       => $orderInfo['passenger_names'],
            'contact_name'          => $orderInfo['contact_name'],
            'contact_telephone'     => $orderInfo['contact_telephone'],
            'contact_email'         => $orderInfo['contact_email'],
            'total_supplier_amount' => $orderInfo['total_amount'],
            'total_customer_amount' => $orderInfo['total_amount'],
            'office'                => config('IBE')->office ?? 'MANUAL',
            'status'                => 2, // 已出票
            'customer_payment_flag' => 2, // 已付款
            'operator_id'           => 1, // TODO: 获取当前用户ID
            'operator_name'         => '手动录入', // TODO: 获取当前用户名
            'created_at'            => time(),
        ]);
    }

    /**
     * 保存乘客信息
     *
     * @param  object  $ticketBookPaxModel
     * @param  int  $orderId
     * @param  array  $passengers
     *
     * @return array
     */
    private function savePassengers($ticketBookPaxModel, int $orderId, array $passengers): array
    {
        $passengerIds = [];

        foreach ($passengers as $passenger) {
            $passengerId = $ticketBookPaxModel->insert([
                'order_id'       => $orderId,
                'person_name'    => $passenger['person_name'],
                'id_number'      => $passenger['id_number'],
                'mobile'         => $passenger['mobile'],
                'passenger_type' => $passenger['passenger_type'],
                'ticket_number'  => $passenger['ticket_number'] ?? '',
                'total_amount'   => $passenger['ticket_price'] +
                                    ($passenger['tax_cn'] ?? 0) +
                                    ($passenger['tax_yq'] ?? 0) +
                                    ($passenger['tax_xt'] ?? 0),
                'ei'             => '手动录入',
                'currency_code'  => 'CNY',
                'payment_type'   => 'CASH',
                'status'         => 1,
                'created_at'     => time(),
            ]);

            $passengerIds[] = $passengerId;
        }

        return $passengerIds;
    }

    /**
     * 保存航段信息
     *
     * @param  object  $ticketBookSegModel
     * @param  int  $orderId
     * @param  array  $segments
     *
     * @return array
     */
    private function saveSegments($ticketBookSegModel, int $orderId, array $segments): array
    {
        $segmentIds = [];

        foreach ($segments as $index => $segment) {
            $segmentId = $ticketBookSegModel->insert([
                'order_id'                => $orderId,
                'rph'                     => $index + 1,
                'departure_datetime'      => $segment['departure_datetime'],
                'arrival_datetime'        => $segment['arrival_datetime'],
                'departure_airport'       => $segment['departure_airport'],
                'arrival_airport'         => $segment['arrival_airport'],
                'departure_terminal'      => $segment['departure_terminal'] ?? '',
                'arrival_terminal'        => $segment['arrival_terminal'] ?? '',
                'code_share_ind'          => 'N',
                'airline'                 => $segment['airline_code'],
                'flight_number'           => $segment['flight_number'],
                'operating_airline'       => $segment['airline_code'],
                'operating_flight_number' => $segment['flight_number'],
                'cabin'                   => $segment['cabin'],
                'aircraft_type'           => $segment['aircraft_type'] ?? '',
                'passenger_number'        => 1, // 每个航段的乘客数量
                'action_code'             => 'HK',
                'segment_type'            => 'O',
                'created_at'              => time(),
            ]);

            $segmentIds[] = $segmentId;
        }

        return $segmentIds;
    }

    /**
     * 保存乘客-航段关联信息
     *
     * @param  object  $ticketBookPaxSegModel
     * @param  array  $passengerIds
     * @param  array  $segmentIds
     * @param  array  $data
     *
     * @return void
     */
    private function savePassengerSegments($ticketBookPaxSegModel, array $passengerIds, array $segmentIds, array $data): void
    {
        foreach ($passengerIds as $passengerIndex => $passengerId) {
            $passenger = $data['passengers'][$passengerIndex];

            foreach ($segmentIds as $segmentIndex => $segmentId) {
                $segment = $data['segments'][$segmentIndex];

                $ticketBookPaxSegModel->insert([
                    'passenger_id'       => $passengerId,
                    'ticket_number'      => $passenger['ticket_number'] ?? '',
                    'flight_number'      => $segment['flight_number'],
                    'departure_datetime' => $segment['departure_datetime'],
                    'departure_airport'  => $segment['departure_airport'],
                    'arrival_airport'    => $segment['arrival_airport'],
                    'marketing_airline'  => $segment['airline_code'],
                    'ticket_status'      => 'OPEN FOR USE',
                    'status'             => 1,
                    'created_at'         => time(),
                ]);
            }
        }
    }

    /**
     * 保存订单明细信息
     *
     * @param  object  $ticketBookOrderDetailModel
     * @param  int  $orderId
     * @param  array  $passengerIds
     * @param  array  $passengers
     *
     * @return void
     */
    private function saveOrderDetails($ticketBookOrderDetailModel, int $orderId, array $passengerIds, array $passengers): void
    {
        foreach ($passengerIds as $index => $passengerId) {
            $passenger = $passengers[$index];

            // 计算各项费用
            $ticketPrice = $passenger['ticket_price'];
            $taxCn       = $passenger['tax_cn'] ?? 0;
            $taxYq       = $passenger['tax_yq'] ?? 0;
            $taxXt       = $passenger['tax_xt'] ?? 0;
            $agencyFee   = $passenger['agency_fee'] ?? 0;

            $totalTax       = $taxCn + $taxYq + $taxXt;
            $supplierAmount = $ticketPrice + $totalTax;
            $customerAmount = $supplierAmount + $agencyFee;

            $ticketBookOrderDetailModel->insert([
                'order_id'               => $orderId,
                'order_passenger_id'     => $passengerId,
                'product_type'           => 1, // 机票
                'product_id'             => 0,
                'supplier_id'            => 0,
                'customer_id'            => 0,
                'supplier_amount'        => $supplierAmount,
                'customer_amount'        => $customerAmount,
                'ticket_marketing_price' => $ticketPrice,
                'ticket_tax_cn'          => $taxCn,
                'ticket_tax_yq'          => $taxYq,
                'ticket_tax_xt'          => $taxXt,
                'ticket_total_price'     => $supplierAmount,
                'supplier_price'         => $ticketPrice,
                'tax_cn'                 => $taxCn,
                'tax_yq'                 => $taxYq,
                'tax_xt'                 => $taxXt,
                'supplier_agency_fee'    => $agencyFee,
                'customer_price'         => $ticketPrice,
                'customer_agency_fee'    => $agencyFee,
                'created_at'             => time(),
            ]);
        }
    }
}
