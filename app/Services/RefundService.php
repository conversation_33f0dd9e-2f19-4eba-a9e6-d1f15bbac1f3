<?php
namespace App\Services;

use App\Models\PnrPassengerModel;
use App\Services\BaseService;
use App\Helpers\Tools;

class RefundService extends BaseService {
    public function __constructor(){}

    public function orderList($params) {
        $page = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 10;
        $pnr = trim($params['pnr']);
        $order_no = trim($params['order_no']);
        $person_name = trim($params['person_name']);
        $order_status = $params['order_status'] ?? '';
        $origin_order_no = trim($params['origin_order_no']);
        $airline = trim($params['airline']);
        $operator_name = trim($params['operator_name']);
        $order_date = $params['order_date'];

        $data = [
            'list' => [],
            'total_status' => [],
            'total' => 0,
            'perPage' => 0,
            'pageCount' => 0,
            'currentPage' => 0,
        ];
        $order_model = model('TicketRefundOrderModel');
        $order_passenger_model = model('TicketRefundPaxModel');
        $order_segment_model = model('TicketRefundSegModel');

        $order_passenger_where = [];
        $pnr_segment_where = [];
        $order_where = [];
        if ($pnr) {
            $order_where['pnr'] = $pnr;
        }
        if ($order_status !== '') {
            $order_where['status'] = $order_status;
        }
        if ($order_no) {
            $order_where['order_no'] = $order_no;
        }
        if ($origin_order_no) {
            $order_where['origin_order_no'] = $origin_order_no;
        }
        if ($operator_name) {
            $order_where['operator_name'] = $operator_name;
        }
        if (!empty($order_date[0])) {
            $order_where['created_at_from'] = $order_date[0];
        }
        if (!empty($order_date[1])) {
            $order_where['created_at_to'] = $order_date[1];
        }
        if ($person_name) {
            $order_passenger_where['person_name'] = $person_name;
        }
        if ($airline) {
            $order_segment_where['airline'] = $airline;
        }
        $order_where['ids'] = [];
        if (!empty($order_passenger_where)) {
            $order_passengers = $order_passenger_model->where($order_passenger_where)->findAll();
            if (empty($order_passengers)) {
                success('成功', $data);
            }
            $order_where['ids'] = array_column($order_passengers, 'order_id');
        }
        if (!empty($order_segment_where)) {
            foreach ($order_segment_where as $k => $v) {
                $order_segment_model->where($k, $v);
            }
            $order_segments = $order_segment_model->findAll();
            if (empty($order_segments)) {
                success('成功', $data);
            }
            $order_where['ids'] = array_merge($order_where['ids'], array_column($order_segments, 'order_id'));
        }

        //分页
        $list = $order_model->paginate_list($order_where, $page, $perPage);
        $pager = $order_model->pager;
        //分组
        $total_status = $order_model->total_status($order_where);
        $data = [
            'list' => $list,
            'total_status' => $total_status,
            'total' => $pager->getTotal(),//总条数
            'perPage' => $pager->getPerPage(),//每页显示条数
            'pageCount' => $pager->getPageCount(),//总页数
            'currentPage' => $pager->getCurrentPage(),//当前页数
        ];

        return $data;
    }

    public function savePrice($params)
    {
        $orderModel             = model('TicketRefundOrderModel');
        $orderPassengerModel    = model('TicketRefundPaxModel');
        $orderDetailModel       = model('TicketRefundOrderDetailModel');

        // 获取订单信息
        $orderId   = intval($params['order_id']);
        if (empty($orderId)) {
            error(0, 'order_id错误');
        }
        $order = $orderModel->find($orderId);
        if (empty($order)) {
            error(0, 'order_id错误');
        }

        // 获取乘客信息
        $inputPassengers = $params['passengers'];
        $orderPassengers = $orderPassengerModel->where('order_id', $orderId)->findAll();
        $inputPassengerIds = array_column($params['passengers'], 'passenger_id');
        $dbPassengerIds = array_column($orderPassengers, 'id');
        // 比较输入的passenger_id和数据库的passenger_id，确保提交的数据是有效的
        if (!empty(array_diff($inputPassengerIds, $dbPassengerIds)) || !empty(array_diff($dbPassengerIds, $inputPassengerIds))) {
            error(0, '数据错误');
        }

        try {
            $this->db->transException(true)->transStart();

            // 更新order_detail
            $total_supplier_amount = 0;
            $total_customer_amount = 0;
            foreach ($inputPassengers as $inputPassenger) {
                $passenger_id                       = $inputPassenger['passenger_id'];
                $ticket_supplier_marketing_price    = $inputPassenger['ticket_supplier_marketing_price'];
                $ticket_supplier_tax_cn             = $inputPassenger['ticket_supplier_tax_cn'];
                $ticket_supplier_tax_yq             = $inputPassenger['ticket_supplier_tax_yq'];
                $ticket_supplier_agency_fee         = $inputPassenger['ticket_supplier_agency_fee'];
                $ticket_supplier_service_fee        = $inputPassenger['ticket_supplier_service_fee'];
                $ticket_supplier_insurance_fee      = $inputPassenger['ticket_supplier_insurance_fee'];
                $ticket_supplier_refund_service_fee = $inputPassenger['ticket_supplier_refund_service_fee'];
                $ticket_supplier_deduction_fee      = $inputPassenger['ticket_supplier_deduction_fee'];
                $ticket_customer_marketing_price    = $inputPassenger['ticket_customer_marketing_price'];
                $ticket_customer_tax_cn             = $inputPassenger['ticket_customer_tax_cn'];
                $ticket_customer_tax_yq             = $inputPassenger['ticket_customer_tax_yq'];
                $ticket_customer_adjust_fee         = $inputPassenger['ticket_customer_adjust_fee'];
                $ticket_customer_service_fee        = $inputPassenger['ticket_customer_service_fee'];
                $ticket_customer_insurance_fee      = $inputPassenger['ticket_customer_insurance_fee'];
                $ticket_customer_refund_service_fee = $inputPassenger['ticket_customer_refund_service_fee'];
                $ticket_customer_deduction_fee      = $inputPassenger['ticket_customer_deduction_fee'];

                $supplier_amount = 0;
                $supplier_amount = bcadd($supplier_amount, $ticket_supplier_marketing_price, 2);
                $supplier_amount = bcadd($supplier_amount, $ticket_supplier_tax_cn, 2);
                $supplier_amount = bcadd($supplier_amount, $ticket_supplier_tax_yq, 2);
                $supplier_amount = bcsub($supplier_amount, $ticket_supplier_agency_fee, 2);
                $supplier_amount = bcadd($supplier_amount, $ticket_supplier_service_fee, 2);
                $supplier_amount = bcadd($supplier_amount, $ticket_supplier_insurance_fee, 2);
                $supplier_amount = bcadd($supplier_amount, $ticket_supplier_refund_service_fee, 2);
                $supplier_amount = bcadd($supplier_amount, $ticket_supplier_deduction_fee, 2);

                $customer_amount = 0;
                $customer_amount = bcadd($customer_amount, $ticket_customer_marketing_price, 2);
                $customer_amount = bcadd($customer_amount, $ticket_customer_tax_cn, 2);
                $customer_amount = bcadd($customer_amount, $ticket_customer_tax_yq, 2);
                $customer_amount = bcadd($customer_amount, $ticket_customer_adjust_fee, 2);
                $customer_amount = bcadd($customer_amount, $ticket_customer_service_fee, 2);
                $customer_amount = bcadd($customer_amount, $ticket_customer_insurance_fee, 2);
                $customer_amount = bcadd($customer_amount, $ticket_customer_refund_service_fee, 2);
                $customer_amount = bcadd($customer_amount, $ticket_customer_deduction_fee, 2);

                $total_supplier_amount += $supplier_amount;
                $total_customer_amount += $customer_amount;
                $order_detail_arr = [
                    'ticket_supplier_marketing_price' => $ticket_supplier_marketing_price,
                    'ticket_supplier_tax_cn' => $ticket_supplier_tax_cn,
                    'ticket_supplier_tax_yq' => $ticket_supplier_tax_yq,
                    'ticket_supplier_agency_fee' => $ticket_supplier_agency_fee,
                    'ticket_supplier_service_fee' => $ticket_supplier_service_fee,
                    'ticket_supplier_insurance_fee' => $ticket_supplier_insurance_fee,
                    'ticket_supplier_refund_service_fee' => $ticket_supplier_refund_service_fee,
                    'ticket_supplier_deduction_fee' => $ticket_supplier_deduction_fee,
                    'ticket_customer_marketing_price' => $ticket_customer_marketing_price,
                    'ticket_customer_tax_cn' => $ticket_customer_tax_cn,
                    'ticket_customer_tax_yq' => $ticket_customer_tax_yq,
                    'ticket_customer_adjust_fee' => $ticket_customer_adjust_fee,
                    'ticket_customer_service_fee' => $ticket_customer_service_fee,
                    'ticket_customer_insurance_fee' => $ticket_customer_insurance_fee,
                    'ticket_customer_refund_service_fee' => $ticket_customer_refund_service_fee,
                    'ticket_customer_deduction_fee' => $ticket_customer_deduction_fee,
                    'supplier_amount' => $supplier_amount,
                    'customer_amount' => $customer_amount,
                ];
                $orderDetailModel->where('order_passenger_id', $passenger_id)
                    ->where('order_id', $orderId)
                    ->set($order_detail_arr)
                    ->update();
            }
            $orderModel->where('id', $orderId)
                ->set([
                        'total_supplier_amount' => $total_supplier_amount,
                        'total_customer_amount' => $total_customer_amount,
                    ])
                ->update();
            $this->db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $this->db->transRollback();
            error(0, $e->getMessage());
        }
    }
}