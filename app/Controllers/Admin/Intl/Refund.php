<?php

namespace App\Controllers\Admin\Intl;

use App\Controllers\AdminController;
use App\Models\TicketRefundOrderModel;
use App\Services\Intl\Refund as RefundService;

class Refund extends AdminController
{
    public RefundService $service;

    public function __construct()
    {
        $this->service = load_service('Intl\Refund');
    }

    /**
     * 退票申请
     *
     * @return void
     * @throws \Exception
     */
    public function apply(): void
    {
        $validation = service('validation');
        $rules      = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $data = $this->service->apply($validData);

        success('成功', $data);
    }

    /**
     * 查询退款费用
     *
     * @return void
     */
    public function fee(): void
    {
        $validation = service('validation');
        $rules      = [
            'ticket_ids' => ['label' => '票号ids', 'rules' => 'required|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData  = $validation->getValidated();
        $ticket_ids = $validData['ticket_ids'];
        /**
         * @var \App\Services\Ticket\Ticket $ticket_service
         */
        $ticket_service = load_service('Ticket\Ticket');
        try {
            //查询价格
            $data = $ticket_service->query_ticket_price($ticket_ids, TicketRefundOrderModel::TICKET_TYPE_INTERNATIONAL);

            success('获取成功', $data);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
    }

    /**
     * 确认退款
     *
     * @return void
     * @throws \Exception
     */
    public function confirm(): void
    {
        $validation = service('validation');
        $rules      = [
            'tickets'               => ['label' => '票号信息', 'rules' => 'required|is_array'],
            'tickets.*.ticket_id'   => [
                'label' => '票号id',
                'rules' => 'required|greater_than[0]',
            ],
            'tickets.*.service_fee' => [
                'label' => '退票手续费',
                'rules' => 'required|greater_than_equal_to[0]',
            ],
            'tickets.*.deduction'   => [
                'label' => '退票费',
                'rules' => 'required|greater_than_equal_to[0]',
            ],
            'order_id'              => ['label' => '订单id', 'rules' => 'required|greater_than[0]'],
            'refund_type'           => ['label' => '退票原因', 'rules' => 'required|in_list[1,2]'],//退票类型：1自愿退票 2非自愿退票
            'contact_name'          => ['label' => '联系人', 'rules' => 'required|max_length[6]|min_length[2]'],
            'contact_telephone'     => ['label' => '联系电话', 'rules' => 'required|max_length[20]|min_length[11]'],
            'contact_email'         => ['label' => '联系邮箱', 'rules' => 'required|max_length[30]|min_length[6]'],
            'is_send_sms'           => ['label' => '通知类型', 'rules' => 'required|in_list[0,1]'],//发送短信：0否 1是
            'remark'                => ['label' => '备注', 'rules' => 'required|max_length[50]|min_length[2]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }

        $validData = $validation->getValidated();
        $data      = $this->service->confirm($validData);

        success('退票成功', $data);
    }

    /**
     * 退款订单列表
     *
     * @return void
     */
    public function refundList(): void
    {
        $validation = service('validation');
        $rules      = [
            'page'            => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page'        => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'pnr'             => ['label' => 'PNR编码', 'rules' => 'permit_empty|max_length[6]|min_length[3]'],
            'order_no'        => ['label' => '改签单号', 'rules' => 'permit_empty|max_length[20]|min_length[3]'],
            'person_name'     => ['label' => '乘客姓名', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_status'    => ['label' => '订单状态', 'rules' => 'permit_empty|greater_than_equal_to[0]'],//小于0则失败
            'origin_order_no' => ['label' => '原订单编号', 'rules' => 'permit_empty|max_length[20]|min_length[13]'],
            'airline'         => ['label' => '航空公司', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'operator_name'   => ['label' => '订票员', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_date'      => ['label' => '退票日期', 'rules' => 'permit_empty|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $data      = $this->service->getOrderList($validData);

        success('成功', $data);
    }

    /**
     * 退款订单详情
     *
     * @return void
     * @throws \Exception
     */
    public function refundDetail(): void
    {
        $validation = service('validation');
        $rules      = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id  = intval($validData['order_id']);
        $data      = $this->service->refundDetail($order_id);

        success('success', $data);
    }
}