<?php

namespace App\Controllers\Admin\Intl;

use App\Controllers\AdminController;
use App\Helpers\Tools\Mobile;
use Exception;

class Order extends AdminController
{
    /**
     * 订单创建
     *
     * @return void
     * @throws Exception
     */
    public function createOrder(): void
    {
        $validation = service('validation');
        $rules      = [
            'flight_segments'                  => ['label' => '航段信息', 'rules' => 'required|is_array'],
            'flight_segments.*.flight_number'  => ['label' => '航班编号', 'rules' => 'required|min_length[2]|max_length[30]'],
            'flight_segments.*.cabin_no'       => ['label' => '舱位', 'rules' => 'required|min_length[1]|max_length[30]'],
            'flight_segments.*.departure_date' => ['label' => '出发日期', 'rules' => 'required|valid_date[Y-m-d]'],
            'flight_segments.*.product_no'     => ['label' => '产品编号', 'rules' => 'required|min_length[1]|max_length[30]'],//产品编号
            'price'                            => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'price.*.type'                     => ['label' => '价格-乘客类型', 'rules' => 'required|min_length[1]|max_length[10]'],
            'price.*.amount'                   => ['label' => '乘客价格', 'rules' => 'required|greater_than[0]'],
            'passengers'                       => ['label' => '乘客信息', 'rules' => 'required|is_array'],
            'passengers.*.surname'             => ['label' => '姓', 'rules' => 'required|min_length[1]|max_length[30]'],
            'passengers.*.given_name'          => ['label' => '名', 'rules' => 'required|min_length[1]|max_length[30]'],
            'passengers.*.passenger_type_code' => ['label' => '乘客类型', 'rules' => 'required|in_list[1,2,3]',],//乘客类型：1成人 2儿童 3婴儿  对应接口：ADT成人 CHD儿童 INF婴儿
            'passengers.*.gender'              => ['label' => '性别', 'rules' => 'required|in_list[1,2]',], //性别：1男 2女
            'passengers.*.birthday'            => ['label' => '出生日期', 'rules' => 'required|valid_date[Y-m-d]',], //出生日期
            'passengers.*.certificate_type'    => ['label' => '证件类型', 'rules' => 'required|in_list[1,2,3,4,5,6,7]',], //证件类型：1身份证 2护照 3 港澳通行证 4台湾通行证 5 回乡证 6 台胞证 7 其他
            'passengers.*.certificate_number'  => ['label' => '证件号码', 'rules' => 'required|min_length[10]|max_length[50]',], // 证件号
            'passengers.*.certificate_country' => ['label' => '证件签发国家', 'rules' => 'required',], //证件签发国家
            'passengers.*.certificate_valid'   => ['label' => '证件有效期', 'rules' => 'required|valid_date[Y-m-d]',], //证件有效期
            'passengers.*.contact_phone'       => ['label' => '乘客联系电话', 'rules' => 'required|min_length[11]|max_length[20]',],
            'passengers.*.nationality'         => ['label' => '国籍', 'rules' => 'required',], //国籍
            'contact_name'                     => ['label' => '预订人姓名', 'rules' => 'required|max_length[6]|min_length[2]'],
            'contact_telephone'                => ['label' => '预订人联系电话', 'rules' => 'required|max_length[20]|min_length[11]'],
            'flight_type'                      => ['label' => '行程类型', 'rules' => 'required|in_list[1,2,3]'],//1单程 2往返 3联程-两航段 4联程-多航段 5多航段
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }

        $validData        = $validation->getValidated();
        $contactTelephone = $validData['contact_telephone'];  //预订人联系电话
        $passengers       = $validData['passengers']; //乘客信息
        $flightSegments   = $validData['flight_segments'];     //航段信息

        //校验预订人手机号
        if (!Mobile::validPhone($contactTelephone)) {
            error(0, "预订人手机号码有误_{$contactTelephone}");
        }

        $flightModel = model('FlightModel');
        $cabinModel  = model('CabinModel');
        //校验航班信息
        $flightNumbers = array_unique(array_column($flightSegments, 'flight_number'));
        $flights       = $flightModel->whereIn('flight_number', $flightNumbers)->findAll();
        $flights       = array_column($flights, null, 'flight_number');
        if (count($flightNumbers) != count($flights)) {
            error(0, '航班号有误,请检查');
        }

        //校验舱位信息
        $airlineCodes = array_unique(array_column($flights, 'airline_code'));
        $cabinList    = $cabinModel->whereIn('airline', $airlineCodes)->findAll();
        $cabinsArr    = [];
        foreach ($cabinList as $val) {
            $cabinsArr[$val['airline'] . $val['cabin']] = $val;
        }

        // 各类型乘客的价格
        $cabinPriceTotal = [];
        foreach ($validData['price'] as $price) {
            $cabinPriceTotal[$price['type']] = $price['amount'];
        }

        /**
         * @var \App\Services\Intl\Order $orderService
         */
        $orderService = load_service('Intl\Order');
        // 航段信息组装 以及 舱位对应的各类型乘客票价总额（防止2个航道同一个舱位编号）
        $flightSegmentArr = $orderService->createFlightSegments($flightSegments, $flights, $cabinsArr);
        // 乘客信息组装
        $passengerArr = $orderService->createPassengers($passengers);

        //留座时间，默认为起飞前两小时，用起飞时间计算
        $firstDepartureDatetime = str_replace('T', ' ', $flightSegmentArr[0]['departure_datetime']); // 确保中间字符是T的情况下，用替换的方式，性能是最好的
        $subtractHours          = strtotime('-2 hours', strtotime($firstDepartureDatetime));
        $subtractHours          = date('Y-m-d H:i:s', $subtractHours);
        $subtractHours          = explode(' ', $subtractHours);
        $ticketTimeLimit        = $subtractHours[0] . 'T' . $subtractHours[1];

        $pnrParams = [
            'flight_segments'   => $flightSegmentArr,//航段信息
            'passengers'        => $passengerArr,//乘客信息
            'airline'           => $airlineCodes[0],// 航空公司CZ
            'ctct'              => $contactTelephone,//预订人联系方式
            'ticket_time_limit' => $ticketTimeLimit,//留座时间
            'tc'                => '', // TC组，可选，可不填或留空
        ];

        $orderId = $orderService->createOrder($validData, $pnrParams, $cabinPriceTotal);

        success('创建成功', ['order_id' => $orderId]);
    }

    /**
     * 确认订单
     *
     * @return void
     * @throws \Exception
     */
    public function confirmOrder(): void
    {
        $validation = service('validation');
        $rules      = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $orderId   = $validData['order_id'];

        /**
         * @var \App\Services\Intl\Order $serv
         */
        $serv = load_service('Intl\Order');
        $data = $serv->confirmOrder($orderId);

        success('成功', $data);
    }

    /**
     * 订单列表
     *
     * @return void
     */
    public function orderList(): void
    {
        $validation = service('validation');
        $rules      = [
            'page'           => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page'       => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'pnr'            => ['label' => 'PNR编码', 'rules' => 'permit_empty|max_length[6]|min_length[3]'],
            'ticket_number'  => ['label' => '票号', 'rules' => 'permit_empty|max_length[20]|min_length[3]'],
            'person_name'    => ['label' => '乘客姓名', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_status'   => ['label' => '订单状态', 'rules' => 'permit_empty|greater_than_equal_to[0]'],//小于0则失败
            'journey_type'   => ['label' => '行程类型', 'rules' => 'permit_empty|in_list[1,2,3]'],//1单程2往返3多程
            'order_no'       => ['label' => '订单编号', 'rules' => 'permit_empty|max_length[20]|min_length[13]'],
            'airline'        => ['label' => '航空公司', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'flight_number'  => ['label' => '航班号', 'rules' => 'permit_empty|max_length[10]|min_length[6]'],
            'customer_type'  => ['label' => '订单类型', 'rules' => 'permit_empty|in_list[1,2,3,4]'],// 订单类型：1自有 2分销
            'order_source'   => ['label' => '订单来源', 'rules' => 'permit_empty|in_list[1,2,3,4,5]'],//订单来源：1系统白屏预订 2分销白屏预订 3差旅白屏预订 4OTA订单 5B2B订单
            'operator_name'  => ['label' => '订票员', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_date'     => ['label' => '下单日期', 'rules' => 'permit_empty|is_array'],
            'departure_date' => ['label' => '出发日期', 'rules' => 'permit_empty|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /**
         * @var \App\Services\Intl\Order $service
         */
        $service = load_service('Intl\Order');
        $data    = $service->getOrderList($validData);

        success('成功', $data);
    }

    /**
     * 订单详情
     *
     * @return void
     * @throws \Exception
     */
    public function OrderDetail(): void
    {
        $validation = service('validation');
        $rules      = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $orderId   = intval($validData['order_id']);

        /**
         * @var \App\Services\Intl\Order $service
         */
        $service = load_service('Intl\Order');
        $data    = $service->orderDetail($orderId);

        success('成功', $data);
    }

    /**
     * 保存国际机票订单价格
     */
    public function savePrice()
    {
        $validation = service('validation');
        $rules      = [
            'order_id'                                 => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
            'passengers'                               => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'passengers.*.passenger_id'                => ['label' => '乘客ID', 'rules' => 'required|greater_than[0]'],
            'passengers.*.is_free'                     => ['label' => '是否免票', 'rules' => 'required|in_list[0,1]'],
            'passengers.*.ticket_marketing_price'      => ['label' => '票面价', 'rules' => 'required|decimal'],
            'passengers.*.ticket_tax_cn'               => ['label' => '机场建设费', 'rules' => 'required|decimal'],
            'passengers.*.ticket_tax_yq'               => ['label' => '税费', 'rules' => 'required|decimal'],
            'passengers.*.ticket_supplier_agency_fee'  => ['label' => '代理费', 'rules' => 'required|decimal'],
            'passengers.*.ticket_supplier_service_fee' => ['label' => '采购服务费', 'rules' => 'required|decimal'],
            'passengers.*.ticket_customer_adjust_fee'  => ['label' => '销售加价/减价', 'rules' => 'required|decimal'],
            'passengers.*.ticket_customer_service_fee' => ['label' => '销售服务费', 'rules' => 'required|decimal'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /**
         * @var \App\Services\Intl\Order $service
         */
        $service = load_service('Intl\Order');
        $service->savePrice($validData);

        success('成功', []);
    }
}