<?php

use App\Controllers\Admin\Intl\Rebook;
use App\Controllers\Admin\Intl\Refund;
use App\Controllers\Admin\Intl\Order;
use App\Controllers\Admin\Intl\Ticket;
use App\Controllers\Admin\Intl\Flight;
use App\Controllers\Admin\Order as DomesticOrder;
use App\Controllers\Admin\Flight as DomesticFlight;
use App\Controllers\Admin\Ticket as DomesticTicket;
use App\Controllers\Admin\Refund as DomesticRefund;
use App\Controllers\Admin\Rebook as DomesticRebook;
use App\Controllers\Home;
use App\Controllers\Admin\Login;
use App\Controllers\Admin\User;
use App\Controllers\Admin\Dashboard;
use App\Controllers\Admin\Department;
use App\Controllers\Admin\Company;
use App\Controllers\Admin\Permission;
use App\Controllers\Admin\Menu;
use App\Controllers\Admin\Role;
use App\Controllers\Admin\Common;
use App\Controllers\Admin\Customer;
use App\Controllers\Admin\CustomerEnterprise;
use App\Controllers\Admin\CustomerOfficial;
use App\Controllers\Admin\CustomerAccount;
use App\Controllers\Admin\CustomerCash;
use App\Controllers\Admin\CustomerCredit;
use App\Controllers\Admin\Payment;
use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', [Home::class, 'index']);
$routes->get('zyztest', [Home::class, 'zyztest']);

$routes->get('/admin/login', [Login::class, 'index']);
$routes->post('/admin/login', [Login::class, 'login']);
$routes->post('/admin/logout', [User::class, 'logout']);
$routes->get('/admin/dashboard', [Dashboard::class, 'index']);
$routes->get('/admin/department/list', [Department::class, 'list']);
$routes->get('/admin/department/info', [Department::class, 'info']);
$routes->post('/admin/department/create', [Department::class, 'create']);
$routes->post('/admin/department/update', [Department::class, 'update']);
$routes->post('/admin/department/editStatus', [Department::class, 'editStatus']);

//公司管理
$routes->get('/admin/company', [Company::class, 'index']);
$routes->get('/admin/company/list', [Company::class, 'list']);
$routes->get('/admin/company/info', [Company::class, 'info']);
$routes->post('/admin/company/create', [Company::class, 'create']);
$routes->post('/admin/company/update', [Company::class, 'update']);
$routes->post('/admin/company/editStatus', [Company::class, 'editStatus']);

//员工管理
$routes->get('/admin/user/list', [User::class, 'list']);
$routes->get('/admin/user/info', [User::class, 'info']);
$routes->post('/admin/user/create', [User::class, 'create']);
$routes->post('/admin/user/update', [User::class, 'update']);
$routes->post('/admin/user/generateUsername', [User::class, 'generateUsername']);
$routes->post('/admin/user/editStatus', [User::class, 'editStatus']);
$routes->post('/admin/user/editPassword', [User::class, 'editPassword']);

//权限管理
$routes->get('/admin/permission/list', [Permission::class, 'list']);
$routes->post('/admin/permission/create', [Permission::class, 'create']);
$routes->post('/admin/permission/update', [Permission::class, 'update']);
$routes->post('/admin/permission/editStatus', [Permission::class, 'editStatus']);
$routes->post('/admin/permission/delete', [Permission::class, 'delete']);
$routes->get('/admin/menu/list', [Menu::class, 'list']);

//角色管理
$routes->get('/admin/role/info', [Role::class, 'info']);
$routes->post('/admin/role/create', [Role::class, 'create']);
$routes->post('/admin/role/update', [Role::class, 'update']);
$routes->post('/admin/role/editPerm', [Role::class, 'editPerm']);
$routes->post('/admin/role/delete', [Role::class, 'delete']);

//公共
$routes->post('/admin/common/listCompany', [Common::class, 'listCompany']);
$routes->post('/admin/common/listDepartment', [Common::class, 'listDepartment']);
$routes->post('/admin/common/listOrg', [Common::class, 'listOrg']);
$routes->post('/admin/common/listRole', [Common::class, 'listRole']);
$routes->post('/admin/common/listUser', [Common::class, 'listUser']);
$routes->post('/admin/common/listAirportCity', [Common::class, 'listAirportCity']);
$routes->post('/admin/common/listAirline', [Common::class, 'listAirline']);
$routes->post('/admin/common/listAirportIntl', [Common::class, 'listAirportIntl']);
$routes->post('/admin/common/listCountryCode', [Common::class, 'listCountryCode']);
$routes->post('/admin/common/listCustomerCashSubject', [Common::class, 'listCustomerCashSubject']);
$routes->post('/admin/common/listPaymentChannel', [Common::class, 'listPaymentChannel']);
$routes->post('/admin/common/listParentCustomer', [Common::class, 'listParentCustomer']);
$routes->post('/admin/common/listBank', [Common::class, 'listBank']);
$routes->post('/admin/common/uploadImages', [Common::class, 'uploadImages']);

//国内机票预订
$routes->post('/admin/flight/domestic_list', [DomesticFlight::class, 'domestic_list']);
$routes->post('/admin/flight/cabin_prices', [DomesticFlight::class, 'cabin_prices']);
$routes->post('/admin/flight/refund_change_rules', [DomesticFlight::class, 'refund_change_rules']);
$routes->post('/admin/flight/check_price', [DomesticFlight::class, 'check_price']);
$routes->post('/admin/order/create_order', [DomesticOrder::class, 'createOrder']);
$routes->post('/admin/order/confirm_order', [DomesticOrder::class, 'confirmOrder']);
$routes->post('/admin/ticket/issue_domestic_ticket', [DomesticTicket::class, 'issue_domestic_ticket']);
$routes->post('/admin/order/domestic_list', [DomesticOrder::class, 'orderList']);
$routes->get('/admin/order/domestic_detail', [DomesticOrder::class, 'orderDetail']);
$routes->post('/admin/order/save_domestic_book_price', [DomesticOrder::class, 'savePrice']);

//国内退款
$routes->post('/admin/order/domestic_refund_list', [DomesticRefund::class, 'orderList']);
$routes->get('/admin/ticket/domestic_refund_apply', [DomesticRefund::class, 'apply']);
$routes->get('/admin/ticket/domestic_refund_fee', [DomesticRefund::class, 'queryRefundFee']);
$routes->post('/admin/ticket/domestic_confirm_refund', [DomesticRefund::class, 'confirmRefund']);
$routes->get('/admin/ticket/domestic_refund_detail', [DomesticRefund::class, 'orderDetail']); // 退票订单详情
$routes->post('/admin/order/save_domestic_refund_price', [DomesticRefund::class, 'savePrice']);

//国内改签
$routes->post('/admin/order/domestic_rebook_list', [DomesticRebook::class, 'orderList']);
$routes->get('/admin/ticket/domestic_change_apply', [DomesticRebook::class, 'apply']); // 获取改签信息
$routes->get('/admin/flight/domestic_reshop', [DomesticFlight::class, 'domestic_reshop']);  // 改签航班查询
$routes->post('/admin/flight/domestic_reissue_price', [DomesticFlight::class, 'domestic_reissue_price']); // 获取改签价格
$routes->post('/admin/ticket/domestic_confirm_change', [DomesticRebook::class, 'confirmChange']); // 改签机票接口
$routes->get('/admin/ticket/domestic_rebook_detail', [DomesticRebook::class, 'orderDetail']); // 改签订单详情
$routes->post('/admin/order/save_domestic_rebook_price', [DomesticRebook::class, 'savePrice']);

//【会员管理】
//散客会员
$routes->get('/admin/customer/list', [Customer::class, 'list']);
$routes->post('/admin/customer/create', [Customer::class, 'create']);
$routes->post('/admin/customer/update', [Customer::class, 'update']);
$routes->post('/admin/customer/detail', [Customer::class, 'detail']);
$routes->post('/admin/customer/import', [Customer::class, 'import']);
$routes->get('/admin/customer/export', [Customer::class, 'export']);
//企业会员
$routes->get('/admin/customerEnterprise/list', [CustomerEnterprise::class, 'list']);
$routes->post('/admin/customerEnterprise/create', [CustomerEnterprise::class, 'create']);
$routes->post('/admin/customerEnterprise/update', [CustomerEnterprise::class, 'update']);
$routes->post('/admin/customerEnterprise/detail', [CustomerEnterprise::class, 'detail']);
$routes->post('/admin/customerEnterprise/import', [CustomerEnterprise::class, 'import']);
$routes->get('/admin/customerEnterprise/export', [CustomerEnterprise::class, 'export']);
//公务会员
$routes->get('/admin/customerOfficial/list', [CustomerOfficial::class, 'list']);
$routes->post('/admin/customerOfficial/create', [CustomerOfficial::class, 'create']);
$routes->post('/admin/customerOfficial/update', [CustomerOfficial::class, 'update']);
$routes->post('/admin/customerOfficial/detail', [CustomerOfficial::class, 'detail']);
$routes->post('/admin/customerOfficial/import', [CustomerOfficial::class, 'import']);
$routes->get('/admin/customerOfficial/export', [CustomerOfficial::class, 'export']);
//会员公共模块
$routes->post('/admin/customer/editStatus', [Customer::class, 'editStatus']);
$routes->post('/admin/customer/getListByType', [Customer::class, 'getListByType']);//根据类型获取会员列表
$routes->post('/admin/customerAccount/get', [CustomerAccount::class, 'get']);//获取会员账户信息

//会员预存金
$routes->post('/admin/customerCash/list', [CustomerCash::class, 'list']);
$routes->post('/admin/customerCash/rechargeApply', [CustomerCash::class, 'rechargeApply']);
$routes->post('/admin/customerCash/listApply', [CustomerCash::class, 'listApply']);//充值/订单付款申请列表
$routes->post('/admin/customerCash/applyDetail', [CustomerCash::class, 'applyDetail']);//充值/订单付款申请详情
$routes->post('/admin/customerCash/audit', [CustomerCash::class, 'audit']);//充值/订单付款审核
//会员授信
$routes->post('/admin/customerCredit/list', [CustomerCredit::class, 'list']);
$routes->post('/admin/customerCredit/adjustAmount', [CustomerCredit::class, 'adjustAmount']);
//订单支付相关
$routes->post('/admin/payment/setCustomerId', [Payment::class, 'setCustomerId']);//设置订单对应的会员
$routes->post('/admin/payment/pay', [Payment::class, 'pay']);//订单支付

/**
 * 国际机票相关
 */
$routes->get('/admin/intl/flight/list', [Flight::class, 'list']); // 查询机票列表
$routes->post('/admin/intl/flight/check_price', [Flight::class, 'checkPrice']); // （检查价格）通过航段查询价格
// 订单相关
$routes->post('/admin/intl/order/create_order', [Order::class, 'createOrder']); // 创建订单
$routes->post('/admin/intl/order/confirm_order', [Order::class, 'confirmOrder']); // 确认订单
$routes->post('/admin/intl/ticket/issue_ticket', [Ticket::class, 'issueTicket']); // 出票
$routes->get('/admin/intl/order/list', [Order::class, 'orderList']); // 订单列表
$routes->get('/admin/intl/order/detail', [Order::class, 'orderDetail']); // 订单详情
$routes->post('/admin/intl/order/savePrice', [Order::class, 'savePrice']); // 保存订单价格
// 退款相关
$routes->get('/admin/intl/refund/apply', [Refund::class, 'apply']); // 退款申请
$routes->get('/admin/intl/refund/fee', [Refund::class, 'fee']); // 退款费用
$routes->post('/admin/intl/refund/confirm', [Refund::class, 'confirm']); // 确认退款
$routes->get('/admin/intl/refund/list', [Refund::class, 'refundList']); // 退款订单列表
$routes->get('/admin/intl/refund/detail', [Refund::class, 'refundDetail']); // 退款订单详情
// 改签相关
$routes->get('/admin/intl/rebook/apply', [Rebook::class, 'apply']); // 改签申请
$routes->post('/admin/intl/rebook/reshop', [Rebook::class, 'reshop']); // 查询改签航班
$routes->post('/admin/intl/rebook/fee', [Rebook::class, 'fee']); // 查询改签费用
$routes->post('/admin/intl/rebook/confirm', [Rebook::class, 'confirm']); // 确认改签
$routes->get('/admin/intl/rebook/list', [Rebook::class, 'list']); // 改签订单列表
$routes->get('/admin/intl/rebook/detail', [Rebook::class, 'detail']); // 改签订单详情

/**
 * 一些独立的接口
 */
$routes->get('admin/order/pull', [DomesticOrder::class, 'pullOrder']); // 拉取订单
$routes->post('admin/order/manual/issue', [DomesticOrder::class, 'manualIssue']); // 手动出票
$routes->post('admin/order/manual/refund', [DomesticOrder::class, 'manualRefund']); // 手动退票
$routes->post('admin/order/manual/rebook', [DomesticOrder::class, 'manualRebook']); // 手动改签