<?php
namespace App\Libraries\Api\IBE;

class Booking {
    protected $ibeConfig;

    public function __construct() {
        $this->ibe_config = config('IBE');
    }

    // 创建PNR
    // 调用参数
    //[
    //    'flight_segments' => [  // 航段，多条记录
    //        [
    //            'rph' => 1, // 航段编号
    //            'departure_datetime' => '2013-05-29T07:00:00', // 出发时间
    //            'arrival_datetime' => '2013-05-29T09:10:00', // 到达时间
    //            'code_share_ind' => false, // 是否共享航班
    //            'flight_number' => '5138', // 航班号
    //            'status' => 'NN', // 行动代码
    //            'segment_type' => 'NORMAL', // 航段类型，默认为NORMAL
    //            'departure_airport' => 'PEK', // 出发机场
    //            'arrival_airport' => 'SHA', // 到达机场
    //            'air_equip_type' => '733', // 机型
    //            'marketing_airline' => 'MU', // 航空公司
    //            'booking_class_avail' => 'Y', // 预订舱位
    //        ],
    //        [
    //            'rph' => 2,
    //            'departure_datetime' => '2013-06-29T07:00:00',
    //            'arrival_datetime' => '2013-06-29T09:10:00',
    //            'code_share_ind' => false,
    //            'flight_number' => '5139',
    //            'status' => 'NN',
    //            'segment_type' => 'NORMAL',
    //            'departure_airport' => 'SHA',
    //            'arrival_airport' => 'PEK',
    //            'air_equip_type' => '733',
    //            'marketing_airline' => 'MU',
    //            'booking_class_avail' => 'Y',
    //        ],
    //    ],
    //    'passengers' => [
    //        [
    //            'rph' => 1, // 乘客编号 =
    //            'passenger_type_code' => 'ADT', // 乘客类型：ADT成人 CHD儿童 INF婴儿
    //            'gender' => 'MALE', // 性别 男
    //            'language_type' => 'ZH', // 语言 ZH中文 EN英文
    //            'surname' => '张学友', // 姓名
    //            'doc' => [ // 证件
    //                'doc_type' => 'NI', // 证件类型：NI 身份证 PP 护照
    //                'doc_id' => '120221197001011150' // 证件号码
    //            ],
    //            'ctcm' => '1366666666', // 乘客联系电话
    //            'infant_traveler_rph' => 3, // 可选，关联婴儿乘客ID，如果没有关联，则可以提供该字段
    //            'tc' => '123456', // TC组，可选，可不填或留空
    //        ],
    //        [
    //            'rph' => 2,
    //            'passenger_type_code' => 'ADT',
    //            'gender' => 'MALE',
    //            'language_type' => 'ZH',
    //            'surname' => '张伟',
    //            'doc' => [
    //                'doc_type' => 'NI',
    //                'doc_id' => '120221197001011151'
    //            ],
    //            'ctcm' => '1366666666',
    //            'tc' => '111111',
    //        ],
    //        [
    //            'rph' => 3,
    //            'passenger_type_code' => 'CHD',
    //            'gender' => 'MALE',
    //            'language_type' => 'ZH',
    //            'surname' => '张伟',
    //            'doc' => [
    //                'doc_type' => 'PP', // 证件类型
    //                'doc_id' => '120221197001011152', // 证件号码
    //                'doc_type_detail' => 'P', // 护照类型：默认P
    //                'doc_issue_country' => 'CN', // 签发国家
    //                'doc_holder_nationality' => 'CN', // 持有人国籍
    //                'birth_date' => '2005-01-01', // 出生日期
    //                'expire_date' => '2030-01-01', // 证件过期日期
    //                'doc_holder_formatted_name' => [
    //                    'given_name' => 'LEI', // 名
    //                    'surname' => 'LI', // 姓
    //                ]
    //            ],
    //            'ctcm' => '1366666666', // 联系电话
    //            'tc' => '111111', // TC组
    //        ],
    //    ],
    //    'airline' => 'MU', // 航空公司
    //    'ctct' => '13666666666', // 预订人联系方式
    //    'ticket_time_limit' => '2013-05-29T00:01:00', // 留座时间，默认为起飞前两小时，用起飞时间计算
    //    'tc' => '', // TC组，可选，可不填或留空
    //];
    //
    // 返回参数
    // 字符串：pnr

    function create_pnr($params) {
        $requestXml = '<OTA_AirBookRQ>
            <POS>
                <Source PseudoCityCode="' . $this->ibe_config->office . '" />
            </POS>
            <AirItinerary>
                <OriginDestinationOptions>
                    <OriginDestinationOption>';
        foreach ($params['flight_segments'] as $flightSegment) {
            $requestXml .= '
                        <FlightSegment RPH="' . $flightSegment['rph'] . '" DepartureDateTime="' . $flightSegment['departure_datetime'] . '"
                            ArrivalDateTime="' . $flightSegment['arrival_datetime'] . '" CodeshareInd="' . $flightSegment['code_share_ind'] . '" FlightNumber="' . $flightSegment['flight_number'] . '"
                            Status="' . $flightSegment['status'] . '" SegmentType="' . $flightSegment['segment_type'] . '">
                            <DepartureAirport LocationCode="' . $flightSegment['departure_airport'] . '" />
                            <ArrivalAirport LocationCode="' . $flightSegment['arrival_airport'] . '" />
                            <Equipment AirEquipType="' . $flightSegment['air_equip_type'] . '" />
                            <MarketingAirline Code="' . $flightSegment['marketing_airline'] . '" />
                            <BookingClassAvail ResBookDesigCode="' . $flightSegment['booking_class_avail'] . '" />
                        </FlightSegment>';
        }
        $requestXml .= '
                    </OriginDestinationOption>
                </OriginDestinationOptions>
            </AirItinerary>
            <TravelerInfo>';
        $passengerTcFlag = 0;
        foreach ($params['passengers'] as $passenger) {
            if (!empty($passenger['tc'])) {
                $passengerTcFlag = 1;
            }
            $requestXml .= '
                <AirTraveler Gender="' . $passenger['gender'] . '" PassengerTypeCode="' . $passenger['passenger_type_code'] . '">
                    <PersonName LanguageType="' . $passenger['language_type'] . '">
                        <Surname>' . $passenger['surname'] . '</Surname>
                    </PersonName>';
                    if ($passenger['doc']['doc_type'] == 'NI') {
                        $requestXml .= '
                    <Document DocType="NI" DocID="' . $passenger['doc']['doc_id'] . '"><!--身份证生日符合儿童规则时，主机会自动加入SSRCHLD，否则需要对预订航段每个航空公司手工写入SSRCHLD-->
                    </Document>';
                    } else if ($passenger['doc']['doc_type'] == 'PP') {
                        $requestXml .= '
                    <Document DocType="PP" DocID="' . $passenger['doc']['doc_id'] . '
                        DocTypeDetail="' . $passenger['doc']['doc_type_detail'] . '"
                        DocIssueContry="' . $passenger['doc']['doc_issue_country'] . '"
                        DocHolderNationality="' . $passenger['doc']['doc_holder_nationality'] . '"
                        BirthDate="' . $passenger['doc']['birth_date'] . '"
                        Gender="' . $passenger['gender'] . '"
                        ExpireDate="' . $passenger['doc']['expire_date'] . '"
                        RPH="1"><!--身份证生日符合儿童规则时，主机会自动加入SSRCHLD，否则需要对预订航段每个航空公司手工写入SSRCHLD-->
                        <DocHolderFormattedName>
                            <GivenName>' . $passenger['doc']['doc_holder_formatted_name']['given_name'] . '</GivenName>
                            <Surname>' . $passenger['doc']['doc_holder_formatted_name']['surname'] . '</Surname>
                        </DocHolderFormattedName>
                    </Document>';
                    }
                    $requestXml .= '
                    <TravelerRefNumber RPH="' . $passenger['rph'] . '"';
                    if (isset($passenger['infant_traveler_rph'])) {
                        $requestXml .= ' InfantTravelerRPH="' . $passenger['infant_traveler_rph'] . '"';
                    }
                    $requestXml .= ' />
                    <Comment>HK</Comment>
                </AirTraveler>';
        }
        $requestXml .= '
                <SpecialReqDetails>
                    <OtherServiceInformations><!--OSI信息-->
                        <OtherServiceInformation Code="OTHS">
                            <Airline Code="' . $params['airline'] . '" />
                            <Text>CTCT' . $params['ctct'] . '</Text><!--CTCT项不需要关联对应旅客，属于非PNR中旅客联系方式，CTCT后数字部分最多30位-->
                        </OtherServiceInformation>';
        foreach ($params['passengers'] as $passenger) {
            $requestXml .= '
                        <OtherServiceInformation Code="OTHS">
                            <Airline Code="' . $params['airline'] . '" />
                            <Text>CTCM' . $passenger['ctcm'] . '</Text><!--CTCM必须关联旅客，CTCM后数字部分最多30位-->
                            <TravelerRefNumber RPH="' . $passenger['rph'] . '" />
                        </OtherServiceInformation>';
        }
        $requestXml .= '
                    </OtherServiceInformations>
                </SpecialReqDetails>
            </TravelerInfo>
            <Ticketing TicketTimeLimit="' . $params['ticket_time_limit'] . '" />
            <TPA_Extensions>
                <ContactInfo>' . $this->ibe_config->telephone . '</ContactInfo>
                <EnvelopType>KI</EnvelopType>
            </TPA_Extensions>';
        if (!empty($params['tc'])) {
            $requestXml .= '
            <TourCodes>
                <TourCode>
                    <Tc>' . $params['tc'] . '</Tc>
                </TourCode>
            </TourCodes>';
        } else if ($passengerTcFlag == 1) {
            $infFlag = 0;
            $arrTc = [];
            $requestXml .= '
            <TourCodes>';
            foreach ($params['passengers'] as $passenger) {
                if (!empty($passenger['tc'])) {
                    if ($passenger['passenger_type_code'] == 'INF' && $infFlag == 0) {
                        $requestXml .= '
                            <TourCode isInfant="true">
                                <Tc>' . $passenger['tc'] . '</Tc>
                            </TourCode>';
                        $infFlag = 1;
                    }
                    if (in_array($passenger['passenger_type_code'], ['ADT', 'CHD'])) {
                        $arrTc[$passenger['tc']][] = $passenger['rph'];
                    }
                }
            }
            if (!empty($arrTc)) {
                foreach ($arrTc as $k => $v) {
                    $requestXml .= '
                    <TourCode>
                        <Tc>' . $k . '</Tc>';
                    foreach ($v as $item) {
                        $requestXml .= '
                        <TravelRefNumber RPH="' . $item . '" />';
                    }
                    $requestXml .= '
                    </TourCode>';
                }
            }
            $requestXml .= '
            </TourCodes>';
        }
        $requestXml .= '
        </OTA_AirBookRQ>';
        // echo $requestXml;


        $xml = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/create_pnr.xml'));
        $pnr = $xml->AirReservation->BookingReferenceID['ID'];
        $pnr = 'J' . mt_rand(10000, 99999);

        return $pnr;
    }

    public function split_pnr($params)
    {
        $requestXml = '<OTA_AirBookModifyRQ>
                    <POS>
                        <Source PseudoCityCode="' . $this->ibe_config->office . '" />
                    </POS>
                    <AirBookModifyRQ ModificationType="PNR_SPLIT">
                        <AirReservation>
                            <TravelerInfo>';
        foreach ($params['passengers'] as $passenger) {
            $requestXml .= '
                                <AirTraveler PassengerTypeCode="' . $passenger['passenger_type_code'] . '" AccompaniedByInfant="' . ($passenger['accompanied_by_infant'] ? 'true' : 'false') . '">
                                    <PersonName LanguageType="' . $passenger['language_type'] . '">
                                        <Surname>' . $passenger['surname'] . '</Surname>
                                    </PersonName>';
            if ($passenger['doc']['doc_type'] == 'NI') {
                $requestXml .= '
                                    <Document DocType="NI" DocID="' . $passenger['doc']['doc_id'] . '"><!--身份证生日符合儿童规则时，主机会自动加入SSRCHLD，否则需要对预订航段每个航空公司手工写入SSRCHLD-->
                                    </Document>';
            } else if ($passenger['doc']['doc_type'] == 'PP') {
                $requestXml .= '
                                    <Document DocType="PP" DocID="' . $passenger['doc']['doc_id'] . '
                                        DocTypeDetail="' . $passenger['doc']['doc_type_detail'] . '"
                                        DocIssueContry="' . $passenger['doc']['doc_issue_country'] . '"
                                        DocHolderNationality="' . $passenger['doc']['doc_holder_nationality'] . '"
                                        BirthDate="' . $passenger['doc']['birth_date'] . '"
                                        Gender="' . $passenger['gender'] . '"
                                        ExpireDate="' . $passenger['doc']['expire_date'] . '"
                                        RPH="1"><!--身份证生日符合儿童规则时，主机会自动加入SSRCHLD，否则需要对预订航段每个航空公司手工写入SSRCHLD-->
                                        <DocHolderFormattedName>
                                            <GivenName>' . $passenger['doc']['doc_holder_formatted_name']['given_name'] . '</GivenName>
                                            <Surname>' . $passenger['doc']['doc_holder_formatted_name']['surname'] . '</Surname>
                                        </DocHolderFormattedName>
                                    </Document>';
            }
            $requestXml .= '
                                    <TravelerRefNumber RPH="' . $passenger['rph'] . '" />
                                </AirTraveler>';
        }
        $requestXml .= '
                            </TravelerInfo>
                        </AirReservation>
                    </AirBookModifyRQ>
                    <AirReservation>
                        <BookingReferenceID ID="' . $params['pnr'] . '" />
                    </AirReservation>
                </OTA_AirBookModifyRQ>';

        //$xml = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/split_pnr.xml'));
        //$new_pnr = (string)$xml->AirReservation->BookingReferenceID['ID'];
        $new_pnr = 'N' . mt_rand(10000, 99999);

        return $new_pnr;
    }

}