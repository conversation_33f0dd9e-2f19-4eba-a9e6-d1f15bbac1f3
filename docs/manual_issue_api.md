# 手动出票接口文档

## 接口信息
- **接口地址**: `POST /admin/order/manual/issue`
- **接口功能**: 用于手动录入机票订单的出票信息
- **Content-Type**: `application/json`

## 请求参数

### 航段信息 (segments)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| flight_number | string | 是 | 航班号，6-10位 |
| departure_datetime | string | 是 | 出发时间，格式：Y-m-d H:i:s |
| departure_airport | string | 是 | 出发机场代码，3位 |
| arrival_datetime | string | 是 | 到达时间，格式：Y-m-d H:i:s |
| arrival_airport | string | 是 | 到达机场代码，3位 |
| departure_terminal | string | 否 | 出发航站楼 |
| arrival_terminal | string | 否 | 到达航站楼 |
| air_equi_type | string | 否 | 机型 |
| cabin | string | 是 | 舱位代码 |

### 乘客信息 (passengers)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| person_name | string | 是 | 乘客姓名 |
| id_number | string | 是 | 证件号 |
| mobile | string | 是 | 手机号 |
| passenger_type | int | 是 | 乘客类型：1成人 2儿童 3婴儿 |
| ticket_number | string | 否 | 票号 |
| ticket_price | decimal | 是 | 票价 |
| tax_cn | decimal | 否 | 机建费 |
| tax_yq | decimal | 否 | 燃油费 |
| tax_xt | decimal | 否 | 其他税费 |
| agency_fee | decimal | 否 | 代理费 |

### 联系人信息
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contact_name | string | 是 | 联系人姓名 |
| contact_mobile | string | 是 | 联系人手机 |
| contact_email | string | 否 | 联系人邮箱 |

### 可选字段
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pnr | string | 否 | PNR编码，如不提供将自动生成 |
| area_type | int | 否 | 区域类型：1国内 2国际，默认1 |

## 请求示例

```json
{
    "segments": [
        {
            "flight_number": "CZ3367",
            "departure_datetime": "2025-07-15 08:30:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-07-15 11:45:00",
            "arrival_airport": "PEK",
            "departure_terminal": "T2",
            "arrival_terminal": "T3",
            "air_equi_type": "A320",
            "cabin": "Y"
        }
    ],
    "passengers": [
        {
            "person_name": "张三",
            "id_number": "110101199001011234",
            "mobile": "13800138000",
            "passenger_type": 1,
            "ticket_number": "7842345678901",
            "ticket_price": 800.00,
            "tax_cn": 50.00,
            "tax_yq": 30.00,
            "tax_xt": 20.00,
            "agency_fee": 10.00
        }
    ],
    "contact_name": "李四",
    "contact_mobile": "13900139000",
    "contact_email": "<EMAIL>",
    "pnr": "ABC123",
    "area_type": 1
}
```

## 响应格式

### 成功响应
```json
{
    "code": 1,
    "msg": "手动出票成功",
    "data": null
}
```

### 错误响应
```json
{
    "code": 0,
    "msg": "错误信息",
    "data": null
}
```

## 业务逻辑说明

1. **参数校验**: 验证所有必填字段和格式
2. **数据查询**: 根据航班号查询航班基础信息，验证机场代码
3. **数据入库**: 
   - 生成订单号和PNR（如未提供）
   - 写入订单主表 `ticket_book_orders`
   - 写入乘客表 `ticket_book_pax`
   - 写入航段表 `ticket_book_seg`
   - 写入乘客-航段关联表 `ticket_book_pax_seg`
   - 写入订单明细表 `ticket_book_order_detail`

## 注意事项

1. 所有数据均由前端上传，不调用IBE接口
2. 支持历史订单补录和特殊出票场景
3. 订单状态直接设置为"已出票"
4. 客户付款状态设置为"已付款"
5. 如果数据库中存在对应航班信息，会自动补全航司代码等信息
6. 机场代码必须在系统中存在，否则会报错

## 错误码说明

- `code: 0` - 请求失败，具体错误信息在msg字段中
- `code: 1` - 请求成功

常见错误：
- 参数验证失败
- 未知的机场代码
- 航班信息不匹配
- 数据库操作失败
