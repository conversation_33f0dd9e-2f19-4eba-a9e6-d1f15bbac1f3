<?php

/**
 * 手动出票接口测试脚本
 * 
 * 使用方法：
 * php tests/manual_issue_test.php
 */

// 测试数据
$testData = [
    'segments' => [
        [
            'flight_number' => 'CZ3367',
            'departure_datetime' => '2025-07-15 08:30:00',
            'departure_airport' => 'CAN',
            'arrival_datetime' => '2025-07-15 11:45:00',
            'arrival_airport' => 'PEK',
            'departure_terminal' => 'T2',
            'arrival_terminal' => 'T3',
            'air_equi_type' => 'A320',
            'cabin' => 'Y'
        ]
    ],
    'passengers' => [
        [
            'person_name' => '张三',
            'id_number' => '110101199001011234',
            'mobile' => '13800138000',
            'passenger_type' => 1,
            'ticket_number' => '7842345678901',
            'ticket_price' => 800.00,
            'tax_cn' => 50.00,
            'tax_yq' => 30.00,
            'tax_xt' => 20.00,
            'agency_fee' => 10.00
        ],
        [
            'person_name' => '李四',
            'id_number' => '110101199002022345',
            'mobile' => '13900139000',
            'passenger_type' => 1,
            'ticket_number' => '7842345678902',
            'ticket_price' => 800.00,
            'tax_cn' => 50.00,
            'tax_yq' => 30.00,
            'tax_xt' => 20.00,
            'agency_fee' => 10.00
        ]
    ],
    'contact_name' => '王五',
    'contact_mobile' => '13700137000',
    'contact_email' => '<EMAIL>',
    'area_type' => 1
];

// 往返航班测试数据
$roundTripData = [
    'segments' => [
        [
            'flight_number' => 'CZ3367',
            'departure_datetime' => '2025-07-15 08:30:00',
            'departure_airport' => 'CAN',
            'arrival_datetime' => '2025-07-15 11:45:00',
            'arrival_airport' => 'PEK',
            'cabin' => 'Y'
        ],
        [
            'flight_number' => 'CZ3368',
            'departure_datetime' => '2025-07-20 14:30:00',
            'departure_airport' => 'PEK',
            'arrival_datetime' => '2025-07-20 17:45:00',
            'arrival_airport' => 'CAN',
            'cabin' => 'Y'
        ]
    ],
    'passengers' => [
        [
            'person_name' => '赵六',
            'id_number' => '110101199003033456',
            'mobile' => '13600136000',
            'passenger_type' => 1,
            'ticket_number' => '7842345678903',
            'ticket_price' => 1200.00,
            'tax_cn' => 100.00,
            'tax_yq' => 60.00,
            'tax_xt' => 40.00
        ]
    ],
    'contact_name' => '孙七',
    'contact_mobile' => '13500135000',
    'contact_email' => '<EMAIL>',
    'pnr' => 'TEST01',
    'area_type' => 1
];

function testManualIssue($data, $testName) {
    echo "\n=== 测试: {$testName} ===\n";
    
    // 这里应该是实际的HTTP请求，但由于我们在测试环境中，
    // 我们只是验证数据结构的正确性
    
    echo "测试数据:\n";
    echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    // 验证必填字段
    $requiredFields = ['segments', 'passengers', 'contact_name', 'contact_mobile'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field])) {
            echo "❌ 缺少必填字段: {$field}\n";
            return false;
        }
    }
    
    // 验证航段信息
    foreach ($data['segments'] as $index => $segment) {
        $requiredSegmentFields = ['flight_number', 'departure_datetime', 'departure_airport', 
                                 'arrival_datetime', 'arrival_airport', 'cabin'];
        foreach ($requiredSegmentFields as $field) {
            if (!isset($segment[$field])) {
                echo "❌ 航段 {$index} 缺少必填字段: {$field}\n";
                return false;
            }
        }
    }
    
    // 验证乘客信息
    foreach ($data['passengers'] as $index => $passenger) {
        $requiredPassengerFields = ['person_name', 'id_number', 'mobile', 
                                   'passenger_type', 'ticket_price'];
        foreach ($requiredPassengerFields as $field) {
            if (!isset($passenger[$field])) {
                echo "❌ 乘客 {$index} 缺少必填字段: {$field}\n";
                return false;
            }
        }
    }
    
    echo "✅ 数据结构验证通过\n";
    
    // 计算总金额
    $totalAmount = 0;
    foreach ($data['passengers'] as $passenger) {
        $passengerTotal = $passenger['ticket_price'] + 
                         ($passenger['tax_cn'] ?? 0) + 
                         ($passenger['tax_yq'] ?? 0) + 
                         ($passenger['tax_xt'] ?? 0);
        $totalAmount += $passengerTotal;
        echo "乘客 {$passenger['person_name']} 总费用: {$passengerTotal}\n";
    }
    echo "订单总金额: {$totalAmount}\n";
    
    return true;
}

// 运行测试
echo "手动出票接口测试\n";
echo "==================\n";

testManualIssue($testData, '单程航班 - 多乘客');
testManualIssue($roundTripData, '往返航班 - 单乘客');

echo "\n测试完成！\n";
echo "\n使用说明:\n";
echo "1. 确保数据库中存在对应的机场代码 (CAN, PEK)\n";
echo "2. 可以通过 POST 请求 /admin/order/manual/issue 接口测试\n";
echo "3. 请求头需要设置 Content-Type: application/json\n";
echo "4. 如果需要实际测试，请使用 curl 或 Postman 等工具\n";

// 生成 curl 测试命令
echo "\ncurl 测试命令示例:\n";
echo "curl -X POST http://your-domain/admin/order/manual/issue \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($testData) . "'\n";
